<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div class="body-top-form">
          <el-form
            ref="searchForm"
            :model="mainObj.searchForm"
            label-width="75px"
            label-position="left"
            class="left"
            inline
          >
            <el-form-item label="用户姓名">
              <el-input
                v-model.trim="mainObj.searchForm.userName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="账号">
              <el-input
                v-model.trim="mainObj.searchForm.account"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="mainObj.searchForm.createTimeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="角色">
              <el-select
                v-model="mainObj.searchForm.roleId"
                placeholder="全部"
                clearable
              >
                <el-option
                  v-for="item in roleList"
                  :key="item.id"
                  :label="item.roleName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select
                v-model="mainObj.searchForm.status"
                placeholder="全部"
                clearable
              >
                <el-option label="正常" :value="1" />
                <el-option label="禁用" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="onSearch"
              >查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <div v-if="addPermission" class="right">
            <div class="main-body-bottom-btn-left">
              <el-button
                type="primary"
                @click="onOperate('add')"
              >新增</el-button>
            </div>
          </div>
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            :header-cell-style="{'background': '#F9F9F9'}"
          >
            <el-table-column
              type="index"
              label="序号"
              width="60"
            />
            <el-table-column
              prop="userName"
              label="用户姓名"
            />
            <el-table-column
              prop="account"
              label="账号"
            />
            <el-table-column
              prop="phone"
              label="手机号"
            />
            <el-table-column
              prop="roleName"
              label="角色"
            />
            <el-table-column
              prop="statusName"
              label="状态"
            />
            <el-table-column
              prop="createTime"
              label="创建时间"
              width="160"
            />
            <el-table-column
              prop="createBy"
              label="创建者"
            />
            <el-table-column
              prop="updateTime"
              label="更新时间"
              width="160"
            />
            <el-table-column
              prop="updateBy"
              label="更新者"
            />
            <el-table-column
              prop="description"
              label="描述"
              width="120"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <div class="description-cell">
                  {{ scope.row.description }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button
                  v-if="updatePermission"
                  v-preventReClick
                  type="text"
                  @click="onOperate('update', scope.row)"
                >修改</el-button>
                <el-button
                  v-if="deletePermission"
                  type="text"
                  @click="onOperate('delete', scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pagesizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 账号新增/编辑弹窗 -->
    <el-dialog
      :title="accountDialogObj.title"
      :visible.sync="accountDialogObj.visible"
      destroy-on-close
      width="600px"
      @close="accountDialogObj.visible = false"
    >
      <el-form
        ref="accountFormRef"
        :model="accountDialogObj.form"
        :rules="accountDialogObj.rules"
        label-width="80px"
        label-position="left"
        class="full-width"
      >
        <el-form-item label="账号" prop="account">
          <el-input
            v-model="accountDialogObj.form.account"
            :disabled="accountDialogObj.type === 'update'"
            maxlength="16"
            placeholder="支持数字、字母、下划线和短线，长度4-16位"
            clearable
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="用户姓名" prop="userName">
          <el-input
            v-model="accountDialogObj.form.userName"
            maxlength="16"
            placeholder="请输入用户姓名"
            clearable
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="accountDialogObj.form.phone"
            maxlength="11"
            placeholder="请输入11位手机号"
            clearable
          />
        </el-form-item>
        <el-form-item label="角色" prop="roleId">
          <el-select
            v-model="accountDialogObj.form.roleId"
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option
              v-for="item in roleList"
              :key="item.id"
              :label="item.roleName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="设置密码" prop="password">
          <el-input
            v-model="accountDialogObj.form.password"
            type="password"
            maxlength="16"
            placeholder="支持数字、字母，长度6-16位"
            clearable
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="accountDialogObj.form.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="accountDialogObj.form.description"
            type="textarea"
            :rows="3"
            maxlength="200"
            placeholder="请输入描述信息"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="accountDialogObj.visible = false">取消</el-button>
        <el-button
          v-preventReClick
          type="primary"
          :loading="isloading"
          @click="onOperate('submit')"
        >保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import accountApi from '@/api/accountApi'
import checkPermission from '@/utils/permission'
import RouteTitle from '@/components/RouteTitle'

const defaultSearchForm = {
  userName: '',
  account: '',
  createTimeRange: [],
  roleId: '',
  status: ''
}

const defaultAccountForm = {
  account: '',
  userName: '',
  phone: '',
  roleId: '',
  password: '',
  status: 1,
  description: ''
}

export default {
  components: {
    RouteTitle
  },
  data() {
    return {
      isloading: false,
      addPermission: checkPermission(['account_add']),
      deletePermission: checkPermission(['account_delete']),
      updatePermission: checkPermission(['account_modify']),
      roleList: [],
      mainObj: {
        list: [],
        pageSize: 10,
        pagesizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      accountDialogObj: {
        visible: false,
        title: '',
        type: '',
        form: Object.assign({}, defaultAccountForm),
        rules: {
          account: [
            { required: true, message: '账号不能为空', trigger: 'blur' },
            { min: 4, max: 16, message: '长度在 4 到 16 个字符', trigger: 'blur' },
            { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含数字、字母、下划线和短线', trigger: 'blur' }
          ],
          userName: [
            { required: true, message: '用户姓名不能为空', trigger: 'blur' },
            { max: 16, message: '长度不能超过16个字符', trigger: 'blur' }
          ],
          phone: [
            { required: true, message: '手机号不能为空', trigger: 'blur' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
          ],
          roleId: [
            { required: true, message: '角色不能为空', trigger: 'change' }
          ],
          password: [
            { required: true, message: '密码不能为空', trigger: 'blur' },
            { min: 6, max: 16, message: '长度在 6 到 16 个字符', trigger: 'blur' },
            { pattern: /^[a-zA-Z0-9]+$/, message: '只能包含数字和字母', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '状态不能为空', trigger: 'change' }
          ]
        }
      }
    }
  },
  created() {
    this.getRoleList()
    this.onSearch()
  },
  methods: {
    checkPermission,

    // 获取角色列表
    getRoleList() {
      accountApi.getRoleList().then((res) => {
        this.roleList = res.data
      })
    },

    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },

    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },

    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },

    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },

    // 获取数据
    fetchData() {
      const params = {
        userName: this.mainObj.searchForm.userName,
        account: this.mainObj.searchForm.account,
        roleId: this.mainObj.searchForm.roleId,
        status: this.mainObj.searchForm.status,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }

      // 处理时间范围
      if (this.mainObj.searchForm.createTimeRange && this.mainObj.searchForm.createTimeRange.length === 2) {
        params.createTimeStart = this.mainObj.searchForm.createTimeRange[0]
        params.createTimeEnd = this.mainObj.searchForm.createTimeRange[1]
      }

      accountApi.getAccountList(params).then((res) => {
        this.mainObj.list = res.data.list
        this.mainObj.total = res.data.total
      })
    },

    // 操作处理
    onOperate(type, row) {
      if (type === 'add') {
        this.accountDialogObj.visible = true
        this.accountDialogObj.type = type
        this.accountDialogObj.title = '新增'
        this.accountDialogObj.form = Object.assign({}, defaultAccountForm)
        this.$nextTick(() => {
          this.$refs.accountFormRef.clearValidate()
        })
      } else if (type === 'update') {
        this.accountDialogObj.visible = true
        this.accountDialogObj.type = type
        this.accountDialogObj.title = '账号修改'
        this.accountDialogObj.form = {
          id: row.id,
          account: row.account,
          userName: row.userName,
          phone: row.phone,
          roleId: row.roleId,
          password: '123456', // 默认密码
          status: row.status,
          description: row.description
        }
        this.$nextTick(() => {
          this.$refs.accountFormRef.clearValidate()
        })
      } else if (type === 'submit') {
        this.$refs.accountFormRef.validate((valid) => {
          if (valid) {
            this.isloading = true
            const apiMethod = this.accountDialogObj.type === 'add' ? accountApi.addAccount : accountApi.modifyAccount

            apiMethod(this.accountDialogObj.form).then((res) => {
              this.$message({
                type: 'success',
                message: res.message
              })
              this.fetchData()
              this.accountDialogObj.visible = false
              this.isloading = false
            }).catch(() => {
              this.isloading = false
            })
          }
        })
      } else if (type === 'delete') {
        this.$confirm(`确定对【${row.account}】进行删除操作吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const params = {
            id: row.id
          }
          accountApi.deleteAccount(params).then((res) => {
            this.$message({
              type: 'success',
              message: res.message
            })
            if ((this.mainObj.total - 1) === ((this.mainObj.currentPage - 1) * this.mainObj.pageSize)) {
              this.mainObj.currentPage--
            }
            this.fetchData()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.right {
  float: right;
}

.left {
  float: left;
}

.description-cell {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: 2.8em;
}

.full-width {
  width: 100%;
}
</style>