<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <!-- 查询表单 -->
        <div class="body-top-form">
          <el-form
            ref="searchForm"
            :model="searchForm"
            label-width="80px"
            label-position="left"
            class="left"
            inline
          >
            <el-form-item label="角色ID">
              <el-input
                v-model.trim="searchForm.roleId"
                placeholder="请输入角色ID"
                clearable
                style="width: 200px;"
              />
            </el-form-item>
            <el-form-item label="角色名称">
              <el-input
                v-model.trim="searchForm.roleName"
                placeholder="请输入角色名称"
                clearable
                style="width: 200px;"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <div v-if="addPermission" class="right">
            <el-button type="primary" @click="handleAdd">新增</el-button>
          </div>
        </div>

        <!-- 数据表格 -->
        <div class="table">
          <el-table
            ref="tableRef"
            :data="tableData"
            fit
            border
            highlight-current-row
            :header-cell-style="{'background': '#F9F9F9'}"
            v-loading="loading"
          >
            <el-table-column prop="roleId" label="角色ID" width="100" />
            <el-table-column prop="roleName" label="角色名称" width="150" />
            <el-table-column prop="createTime" label="创建时间" width="180" />
            <el-table-column prop="creator" label="创建者" width="120" />
            <el-table-column prop="updateTime" label="更新时间" width="180" />
            <el-table-column prop="updater" label="更新者" width="120" />
            <el-table-column prop="remark" label="备注" min-width="200">
              <template slot-scope="scope">
                <div class="remark-cell">
                  <el-tooltip
                    v-if="scope.row.remark && scope.row.remark.length > 50"
                    :content="scope.row.remark"
                    placement="top"
                  >
                    <span>{{ scope.row.remark | truncate(50) }}</span>
                  </el-tooltip>
                  <span v-else>{{ scope.row.remark || '-' }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template slot-scope="scope">
                <el-button
                  v-if="updatePermission"
                  type="text"
                  @click="handleEdit(scope.row)"
                >修改</el-button>
                <el-button
                  v-if="deletePermission"
                  type="text"
                  style="color: #f56c6c;"
                  @click="handleDelete(scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            :current-page="pagination.currentPage"
            :page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 新增/修改弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      destroy-on-close
      @close="handleDialogClose"
    >
      <el-form
        ref="roleForm"
        :model="roleForm"
        :rules="formRules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item v-if="dialogType === 'edit'" label="角色ID">
          <el-input v-model="roleForm.roleId" disabled />
        </el-form-item>
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model="roleForm.roleName"
            placeholder="请输入角色名称"
            maxlength="16"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="roleForm.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="权限配置">
          <div class="permission-tree-container">
            <div class="tree-header">
              <el-checkbox
                v-model="checkAll"
                :indeterminate="isIndeterminate"
                @change="handleCheckAllChange"
              >全选</el-checkbox>
            </div>
            <el-tree
              ref="permissionTree"
              :data="permissionTreeData"
              :props="treeProps"
              show-checkbox
              node-key="id"
              default-expand-all
              :check-strictly="false"
              @check-change="handleTreeCheckChange"
            />
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import checkPermission from '@/utils/permission'
import { getRoleList, createRole, updateRole, deleteRole, getPermissionTree } from '@/api/role'

export default {
  name: 'RoleManage',
  components: {
    RouteTitle
  },
  filters: {
    truncate(value, length) {
      if (!value) return ''
      if (value.length <= length) return value
      return value.substring(0, length) + '...'
    }
  },
  data() {
    return {
      // 权限控制
      addPermission: checkPermission(['role_add']),
      updatePermission: checkPermission(['role_update']),
      deletePermission: checkPermission(['role_delete']),
      
      // 查询表单
      searchForm: {
        roleId: '',
        roleName: ''
      },
      
      // 表格数据
      tableData: [],
      loading: false,
      
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      
      // 弹窗相关
      dialogVisible: false,
      dialogType: 'add', // add | edit
      submitLoading: false,
      
      // 表单数据
      roleForm: {
        roleId: '',
        roleName: '',
        remark: '',
        permissions: []
      },
      
      // 表单验证规则
      formRules: {
        roleName: [
          { required: true, message: '请输入角色名称', trigger: 'blur' },
          { max: 16, message: '角色名称不能超过16个字符', trigger: 'blur' }
        ]
      },
      
      // 权限树相关
      permissionTreeData: [],
      treeProps: {
        children: 'children',
        label: 'name'
      },
      checkAll: false,
      isIndeterminate: false
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogType === 'add' ? '新增' : '修改'
    }
  },
  created() {
    this.fetchData()
    this.getPermissionTreeData()
  },
  methods: {
    // 获取列表数据
    async fetchData() {
      this.loading = true
      try {
        const params = {
          ...this.searchForm,
          pageNum: this.pagination.currentPage,
          pageSize: this.pagination.pageSize
        }
        const { data } = await getRoleList(params)
        this.tableData = data.list || []
        this.pagination.total = data.total || 0
      } catch (error) {
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取权限树数据
    async getPermissionTreeData() {
      try {
        const { data } = await getPermissionTree()
        this.permissionTreeData = data || []
      } catch (error) {
        this.$message.error('获取权限数据失败')
      }
    },
    
    // 查询
    onSearch() {
      this.pagination.currentPage = 1
      this.fetchData()
    },
    
    // 重置
    onReset() {
      this.searchForm = {
        roleId: '',
        roleName: ''
      }
      this.pagination.currentPage = 1
      this.fetchData()
    },
    
    // 分页
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.currentPage = 1
      this.fetchData()
    },
    
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.fetchData()
    },
    
    // 新增
    handleAdd() {
      this.dialogType = 'add'
      this.dialogVisible = true
      this.resetForm()
    },
    
    // 修改
    async handleEdit(row) {
      this.dialogType = 'edit'
      this.dialogVisible = true
      this.roleForm = {
        roleId: row.roleId,
        roleName: row.roleName,
        remark: row.remark || '',
        permissions: row.permissions || []
      }
      
      // 设置权限树选中状态
      this.$nextTick(() => {
        this.$refs.permissionTree.setCheckedKeys(row.permissions || [])
        this.updateCheckAllStatus()
      })
    },
    
    // 删除
    handleDelete(row) {
      // 检查是否关联账号（这里需要调用实际的检查接口）
      this.checkRoleAssociation(row)
    },
    
    // 检查角色关联
    async checkRoleAssociation(row) {
      try {
        // 这里应该调用实际的检查接口
        const hasAssociation = false // 模拟数据
        
        if (hasAssociation) {
          this.$alert('当前角色已关联账号，请调整后再删除。', '提示', {
            confirmButtonText: '确定',
            type: 'warning'
          })
        } else {
          this.$confirm(`确定对【${row.roleName}】进行删除操作吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.confirmDelete(row)
          })
        }
      } catch (error) {
        this.$message.error('检查角色关联失败')
      }
    },
    
    // 确认删除
    async confirmDelete(row) {
      try {
        await deleteRole(row.roleId)
        this.$message.success('删除成功')
        this.fetchData()
      } catch (error) {
        this.$message.error('删除失败')
      }
    },
    
    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.roleForm.validate()
        
        this.submitLoading = true
        const checkedKeys = this.$refs.permissionTree.getCheckedKeys()
        const formData = {
          ...this.roleForm,
          permissions: checkedKeys
        }
        
        if (this.dialogType === 'add') {
          await createRole(formData)
          this.$message.success('新增成功')
        } else {
          await updateRole(formData.roleId, formData)
          this.$message.success('修改成功')
        }
        
        this.dialogVisible = false
        this.fetchData()
      } catch (error) {
        if (error !== false) { // 表单验证失败时不显示错误消息
          this.$message.error(this.dialogType === 'add' ? '新增失败' : '修改失败')
        }
      } finally {
        this.submitLoading = false
      }
    },
    
    // 全选/取消全选
    handleCheckAllChange(val) {
      if (val) {
        this.$refs.permissionTree.setCheckedNodes(this.getAllTreeNodes())
      } else {
        this.$refs.permissionTree.setCheckedKeys([])
      }
      this.isIndeterminate = false
    },
    
    // 树节点选中状态改变
    handleTreeCheckChange() {
      this.updateCheckAllStatus()
    },
    
    // 更新全选状态
    updateCheckAllStatus() {
      const checkedCount = this.$refs.permissionTree.getCheckedKeys().length
      const totalCount = this.getAllTreeNodes().length
      
      this.checkAll = checkedCount === totalCount
      this.isIndeterminate = checkedCount > 0 && checkedCount < totalCount
    },
    
    // 获取所有树节点
    getAllTreeNodes() {
      const nodes = []
      const traverse = (data) => {
        data.forEach(item => {
          nodes.push(item)
          if (item.children && item.children.length > 0) {
            traverse(item.children)
          }
        })
      }
      traverse(this.permissionTreeData)
      return nodes
    },
    
    // 重置表单
    resetForm() {
      this.roleForm = {
        roleId: '',
        roleName: '',
        remark: '',
        permissions: []
      }
      this.checkAll = false
      this.isIndeterminate = false
      
      this.$nextTick(() => {
        if (this.$refs.roleForm) {
          this.$refs.roleForm.clearValidate()
        }
        if (this.$refs.permissionTree) {
          this.$refs.permissionTree.setCheckedKeys([])
        }
      })
    },
    
    // 弹窗关闭
    handleDialogClose() {
      this.resetForm()
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.body-top-form {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  
  .left {
    flex: 1;
  }
  
  .right {
    flex-shrink: 0;
  }
}

.table {
  margin-bottom: 20px;
}

.remark-cell {
  line-height: 1.5;
  max-height: 3em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.permission-tree-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
  
  .tree-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }
}

.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

::v-deep .el-tree-node__content {
  height: 32px;
  line-height: 32px;
}

::v-deep .el-tree-node__label {
  font-size: 14px;
}
</style>
