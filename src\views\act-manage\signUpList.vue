<template>
  <div>
    <RouteTitle />
    <!-- 条件查询 -->
    <div class="main-body">
      <div class="main-body-top">
        <!-- 页面条件搜索 -->
        <dynamic-query
          :data="queryConfig"
          @queryChanged="handleQueryDataChanged"
          @search="handleSearch"
        >
          <!-- <template #handleExport="{queryData}">
            <el-button
              type="primary"
              :loading="exportLoading"
              @click="handleExport(queryData)"
            >导出报名表</el-button>
          </template> -->
        </dynamic-query>
        <!-- 页面列表数据 + 分页条 -->
        <dynamic-table
          :config="tableConfig"
          :data="tableData"
          :is-loading.sync="tableLoading"
          @pagination="handlePageChange"
        >
          <!-- S 处理表格自定义插槽 -->
          <template v-slot:operate="{ rowData, index }">
            <el-button
              type="text"
              @click.stop="handleMore(rowData, index)"
            >查看更多</el-button>
          </template>
        </dynamic-table>
      </div>
    </div>
    <!-- 配置dialog -->
    <SignUpDialog
      v-if="dialogVisible"
      :dialog-visible="dialogVisible"
      :config="dialogConfig"
      @handleClose="handleClose"
    />
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import DynamicQuery from '@/components/dynamic/Query.vue'
import DynamicTable from '@/components/dynamic/Table.vue'
import SignUpDialog from './components/signUpDialog.vue'
import { signExport } from '@/api/actManage'
import mixin from '../mixin'
import { downloadFile } from '@/utils'
export default {
  components: {
    RouteTitle,
    DynamicQuery,
    DynamicTable,
    SignUpDialog
  },
  mixins: [mixin],
  data() {
    return {
      queryUrl: '/blindDate/signUpInfoList',
      dialogVisible: false,
      exportLoading: false,
      dialogConfig: {},
      queryConfig: {
        queryItem: [
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '活动名称:',
            model: 'actTitle',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '活动ID:',
            model: 'actId',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'date', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '报名时间:',
            model: 'signUpDateArr',
            config: {
              type: 'datetimerange', // 时间区间 - 返回数组 需业务重写字段
              separator: '-',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
              format: 'yyyy-MM-dd HH:mm:ss',
              pickerOption: {
                // disabledDate(time) {
                // 	return time.getTime() > Date.now()-24*60*60*1000;
                // }
              }
            }
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '报名用户:',
            model: 'registerName',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '手机号:',
            model: 'registerTel',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '身份证号:',
            model: 'registerIdCard',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'option'
          }
          // {
          //   type: 'option',
          //   slotName: 'handleExport'
          // }
        ]
      },
      // 列表字段
      tableConfig: {
        tableColumn: [
          {
            prop: '',
            label: '序号',
            isIndex: true
          },
          {
            prop: 'registerName',
            label: '报名用户'
          },
          {
            prop: 'registerTel',
            label: '手机号'
          },
          {
            prop: 'registerIdCard',
            label: '身份证号'
          },
          {
            prop: 'actId',
            label: '活动ID'
          },
          {
            prop: 'actTitle',
            label: '活动名称',
            minWidth: 200
          },
          {
            prop: 'createTime',
            label: '报名时间'
          },
          {
            prop: '',
            label: '操作',
            slotName: 'operate', // 操作 - 自定义操作，和operateList二选一
            fixed: 'right',
            minWidth: 160
          }
        ]
      },

      // 列表数据
      tableData: {
        list: [],
        // 分页数据
        pageTotal: 0, // 列表总数
        pageSize: 10, // 每页条数
        pageNum: 1 // 当前页码
      }
    }
  },
  methods: {
    handleMore(rowData, index) {
      this.dialogVisible = true
      this.dialogConfig = {
        title: '查看更多',
        formData: rowData,
        index
      }
    },
    // 导出
    handleExport(data) {
      const params = {
        ...data
      }
      if (params.signUpDateArr) {
        params.signUpStartTime = params.signUpDateArr[0]
        params.signUpEndTime = params.signUpDateArr[1]
        delete params.signUpDateArr
      }
      this.exportLoading = true
      signExport(data)
        .then(res => {
          downloadFile(
            res.data,
            '报名表',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          )
        })
        .finally(() => {
          this.exportLoading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.header-tab {
  display: inline-block;
  margin-left: 20px;
  font-weight: normal;
  font-size: 16px;
  .active {
    color: #1890ff;
    padding-bottom: 5px;
    border-bottom: 3px solid #1890ff;
  }
  .tab-line {
    margin: 0 10px;
  }
}
</style>
