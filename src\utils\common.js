export function download(res) {
  const downloadElement = document.createElement('a')
  const downloadUrl = window.URL.createObjectURL(res.data)
  downloadElement.href = downloadUrl
  const name = res.headers['content-disposition']
  downloadElement.download = decodeURIComponent(name.substring(20, name.length))
  document.body.appendChild(downloadElement)
  downloadElement.click()
  document.body.removeChild(downloadElement)
  window.URL.revokeObjectURL(downloadUrl)
}
// 数据服务下载
export function servicedownload(res) {
  const downloadElement = document.createElement('a')
  const downloadUrl = window.URL.createObjectURL(res.data)
  downloadElement.href = downloadUrl
  // const name = res.headers['content-disposition']
  const name = res.headers.filename
  // const name = window.decodeURIComponent(filename)
  downloadElement.download = decodeURIComponent(name.substring(0, name.length))
  document.body.appendChild(downloadElement)
  downloadElement.click()
  document.body.removeChild(downloadElement)
  window.URL.revokeObjectURL(downloadUrl)
}

