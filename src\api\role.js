import request from '@/utils/request'

// 获取角色列表 - 使用mock接口
export function getRoleList(params) {
  return request({
    url: '/system/role/list',
    method: 'get',
    params
  })
}

// 创建角色 - 使用mock接口
export function createRole(data) {
  return request({
    url: '/system/role',
    method: 'post',
    data
  })
}

// 更新角色 - 使用mock接口
export function updateRole(roleId, data) {
  return request({
    url: `/system/role/${roleId}`,
    method: 'put',
    data
  })
}

// 删除角色 - 使用mock接口
export function deleteRole(roleId) {
  return request({
    url: `/system/role/${roleId}`,
    method: 'delete'
  })
}

// 获取权限树 - 使用mock接口
export function getPermissionTree() {
  return request({
    url: '/system/permission/tree',
    method: 'get'
  })
}

// 检查角色关联 - 使用mock接口
export function checkRoleAssociation(roleId) {
  return request({
    url: `/system/role/${roleId}/association`,
    method: 'get'
  })
}
