<template>
  <div class="upload-box">
    <div v-if="imgUrl && !loading" class="img-box">
      <div class="image">
        <el-image style="width: 100%; height: 100%" :src="imgUrl" fit="fill" />
      </div>
      <div v-if="!isEdit" class="demo-upload-list-cover">
        <i class="el-icon-delete" @click="removeImg" />
      </div>
    </div>
    <el-upload
      ref="upload_img"
      v-loading="loading"
      style="width: 148px; height: 148px"
      action="#"
      :accept="accept"
      list-type="picture-card"
      :show-file-list="false"
      :on-exceed="handleExceed"
      :limit="limit"
      :http-request="uploadSectionFile"
    >
      <div v-if="!imgUrl" class="el-upload__text">
        <i class="el-icon-plus" />
        <span>{{ imgTxt }}</span>
      </div>
    </el-upload>

    <div slot="tip" class="el-upload__tip">
      <span v-if="!$slots.tips"> {{ tips }}</span>
      <slot v-else name="tips" />
    </div>
  </div>
</template>

<script>
import { uploadFile } from '@/api/community'

export default {
  name: 'ImgUpload',
  components: {
    // ElImageViewer,
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    src: {
      type: String,
      default: ''
    },
    accept: {
      type: String,
      default: 'image/jpeg,image/jpg,image/png,image/gif'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    imgTxt: {
      type: String,
      default: ''
    },
    tips: {
      type: String,
      default: '只能上传jpeg、jpg、png、gif 格式的图片，图片大小在2M以内。'
    },
    fileSize: {
      type: Number,
      default: 2
    },
    fileType: {
      type: String,
      default: 'MB'
    },
    proportion: {
      // 图片比例
      type: Number,
      default: 0
    },
    imgWidth: {
      type: Number,
      default: 0
    },
    imgHeight: {
      type: Number,
      default: 0
    },
    imgSize: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      limit: 1,
      loading: false,
      imgUrl: '',
      showBig: false
      // file_list: [],
    }
  },

  computed: {
    isEdit() {
      return this.disabled
    }
  },
  watch: {
    value: {
      handler(val) {
        this.imgUrl = val
      },
      immediate: true
    }
  },
  mounted() {
  },
  methods: {
    handleExceed() {
      this.$message.error('图片最多上传一张，请删除再重新上传！')
    },
    // 文件列表移除文件时的钩子
    removeImg(file, fileList) {
      this.dialogImageUrl = ''
      this.imgUrl = ''
      this.$emit('input', '')
      this.$emit('delImg', '')
      this.$refs.upload_img.clearFiles()
      // this.file_list = [];
    },
    handlePictureCardPreview() {
      this.showBig = true
    },
    closeViewer() {
      this.showBig = false
    },
    uploadSectionFile(params) {
      // 自定义上传方法
      // console.log(params);
      const that = this
      const file = params.file // 获取上传的文件
      const fileType = file.type // 获取文件类型
      const isImage = fileType.indexOf('image') !== -1 // 判断是否是图片类型
      const file_url = that.$refs.upload_img.uploadFiles[0].url
      // console.log(file,fileType,isImage,file_url,that.$refs.upload_img);
      let isLt
      if (this.fileType === 'MB') {
        isLt = file.size / 1024 / 1024 < this.fileSize
      } else {
        isLt = file.size / 1024 < this.fileSize
      }
      if (!isLt) {
        // 判断大小
        that.$message.error(
          `上传图片的大小不能超过 ${this.fileSize}${this.fileType}!`
        )
        that.$refs.upload_img.uploadFiles = [] // 不符合就清空已选择的图片
        return
      }
      if (!isImage) {
        // 文件格式
        that.$message.error('只能上传图片文件')
        that.$refs.upload_img.uploadFiles = [] // 不符合就清空已选择的图片
        return
      }
      if (isImage) {
        var img = new Image()
        img.src = file_url
        img.onload = function() {
          if (that.proportion) {
            if (img.width / img.height !== that.proportion) {
              // 判断比例
              that.$message.error(`上传图片比例不正确，请选择正确图片尺寸`)
              that.$refs.upload_img.uploadFiles = [] // 不符合就清空已选择的图片
              return
            }
          }
          if (JSON.stringify(that.imgSize) !== '{}') {
            // 根据对比类型进行数据判断
            if (
              (that.imgSize.contrast === '==' &&
                (img.width / img.height !== that.imgSize.imgWidth / that.imgSize.imgHeight)) ||
              (that.imgSize.contrast === '>=' &&
                (img.width < that.imgSize.imgWidth ||
                  img.height < that.imgSize.imgHeight)) ||
              (that.imgSize.contrast === '<=' &&
                (img.width > that.imgSize.imgWidth ||
                  img.height > that.imgSize.imgHeight))
            ) {
              that.$message.error(`上传图片尺寸不正确，请选择正确图片尺寸`)
              that.$refs.upload_img.uploadFiles = [] // 不符合就清空已选择的图片
              return
            }
          }
          //  一切验证通过后调用上传方法
          that.uploadFile(file)
        }
      }
    },
    uploadFile(file) {
      this.loading = true
      const that = this
      const formData = new FormData()
      formData.append('file', file)
      uploadFile(formData)
        .then(res => {
          if (res.code === 200) {
            that.$emit('input', res.data.url)
            if (res.data.id) {
              that.$emit('changeId', res.data.id)
            }
          }
        })
        .finally(() => {
          that.$refs.upload_img.clearFiles()
          that.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-box {
  position: relative;
  .img-box {
    position: absolute;
    width: 148px;
    height: 148px;
    text-align: center;
    .image {
      width: 100%;
      height: 100%;
      overflow: hidden;
      background-repeat: no-repeat !important;
      background-size: cover;
      background-position: center;
    }
    .demo-upload-list-cover {
      display: none;
      width: 148px;
      height: 148px;
      position: absolute;
      top: 0;
      bottom: 0;
      text-align: center;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.6);
      font-size: 26px;
      color: #fff;
      line-height: 148px;
      i {
        cursor: pointer;
      }
    }
    &:hover .demo-upload-list-cover {
      display: block;
    }
  }
  .el-upload__text {
    position: relative;
    span {
      position: absolute;
      display: block;
      width: 100%;
      text-align: center;
      top: 20px;
    }
  }
}
::v-deep .el-upload-list--picture-card {
  display: none;
}
</style>
