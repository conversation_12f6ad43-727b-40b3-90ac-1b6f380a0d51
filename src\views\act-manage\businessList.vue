<template>
  <div>
    <RouteTitle />
    <!-- 条件查询 -->
    <div class="main-body">
      <div class="main-body-top">
        <!-- 页面条件搜索 -->
        <dynamic-query :data="queryConfig" @queryChanged="handleQueryDataChanged" @search="handleSearch">
          <template #handleAdd>
            <el-button type="primary" icon="el-icon-plus" @click="handleAddOrEdit('add')">新增商户</el-button>
          </template>
        </dynamic-query>
        <!-- 页面列表数据 + 分页条 -->
        <dynamic-table
          :config="tableConfig"
          :data="tableData"
          :is-loading.sync="tableLoading"
          @pagination="handlePageChange"
        >
          <!-- S 处理表格自定义插槽 -->
          <template v-slot:operate="{ rowData, index }">
            <el-button type="text" @click.stop="handleAddOrEdit('edit', rowData)">编辑</el-button>
          </template>
        </dynamic-table>
      </div>
    </div>
    <!-- 新增编辑查看 弹窗 -->
    <dynamic-dialog-form
      v-if="dialogVisible"
      ref="dynamicDialogForm"
      :show.sync="dialogVisible"
      :form-config="formConfig"
      :btn-loading="saveBtnLoading"
      @event="handleDialogEvent"
    >
      <!-- 查看弹窗 按钮重写 -->
      <template #checkBtn>
        <el-button type="primary" plain @click="dialogVisible = false">关闭</el-button>
      </template>
    </dynamic-dialog-form>
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import DynamicQuery from '@/components/dynamic/Query.vue'
import DynamicTable from '@/components/dynamic/Table.vue'
import DynamicDialogForm from '@/components/dynamic/DialogForm'
import validators from '@/utils/validate'
import { businessAddOrEdit } from '@/api/actManage'

import mixin from '../mixin'
export default {
  components: {
    RouteTitle,
    DynamicQuery,
    DynamicTable,
    DynamicDialogForm
  },
  mixins: [mixin],
  data() {
    return {
      queryUrl: '/business/list',
      commitUrl: 'add',
      saveBtnLoading: false,
      dialogVisible: false,
      dialogType: 'add',
      queryConfig: {
        queryItem: [
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '商户ID:',
            model: 'id',
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '商户名称:',
            model: 'merName',
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '联系人:',
            model: 'contacts',
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '联系电话:',
            model: 'mobile',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'option'
          },
          {
            type: 'option',
            slotName: 'handleAdd'
          }
        ]
      },
      // 列表字段
      tableConfig: {
        tableColumn: [
          {
            prop: '',
            label: '序号',
            isIndex: true
          },
          {
            prop: 'id',
            label: '商户ID'
          },
          {
            prop: 'merName',
            label: '商户名称'
          },
          {
            prop: 'companyAddress',
            label: '公司地址'
          },
          {
            prop: 'contacts',
            label: '联系人'
          },
          {
            prop: 'mobile',
            label: '联系电话'
          },
          {
            prop: 'updateTime',
            label: '更新时间'
          },
          {
            prop: 'operator',
            label: '操作者'
          },
          {
            prop: '',
            label: '操作',
            slotName: 'operate', // 操作 - 自定义操作，和operateList二选一
            fixed: 'right',
            minWidth: 160
          }
        ]
      },

      // 列表数据
      tableData: {
        list: [],
        // 分页数据
        pageTotal: 0, // 列表总数
        pageSize: 10, // 每页条数
        pageNum: 1 // 当前页码
      },
      formData: {},
      formItem: [
        {
          type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
          label: '商户名称',
          model: 'merName'
        },
        {
          type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
          label: '公司地址',
          model: 'companyAddress'
        },
        {
          type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
          label: '联系人',
          model: 'contacts'
        },
        {
          type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
          label: '联系电话',
          model: 'mobile'
        }
      ],
      formRule: {
        merName: [validators.required('商户名称')],
        companyAddress: [validators.required('公司地址')],
        contacts: [validators.required('联系人')],
        mobile: [validators.required('联系电话')]
      },
      formConfig: {}
    }
  },
  methods: {
    handleAddOrEdit(type, rowData) {
      const _this = this
      _this.commitUrl = type === 'add' ? 'add' : 'edit'
      _this.dialogType = type
      _this.formData = type === 'edit' ? rowData : {}
      _this.formConfig = {
        title: type === 'add' ? '新增商户' : '编辑商户',
        formItem: _this.formItem,
        formRule: _this.formRule,
        formData: _this.formData
      }
      this.dialogVisible = true
    },
    // 弹窗确定事件
    handleDialogEvent: function() {
      const _this = this
      this.$refs.dynamicDialogForm.$refs.dynamicForm.$refs.dialogForm.validate(
        valid => {
          if (!valid) return
          _this.saveBtnLoading = true
          businessAddOrEdit(_this.formData, _this.commitUrl).then(res => {
            _this.$message.success(res.msg)
            _this.dialogVisible = false
            _this.getList()
          }).finally(() => {
            _this.saveBtnLoading = false
          })
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.header-tab {
  display: inline-block;
  margin-left: 20px;
  font-weight: normal;
  font-size: 16px;

  .active {
    color: #1890ff;
    padding-bottom: 5px;
    border-bottom: 3px solid #1890ff;
  }

  .tab-line {
    margin: 0 10px;
  }
}
</style>
