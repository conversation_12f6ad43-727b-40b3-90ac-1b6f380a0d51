<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <div class="body-top-form">
          <el-form
            ref="searchForm"
            class="left"
            :model="mainObj.searchForm"
            label-position="left"
            inline
          >
            <el-form-item label="状态：">
              <el-select
                v-model="mainObj.searchForm.status"
                placeholder="请选择"
                clearable
              >
                <!-- <el-option label="全部" value="" />
                <el-option label="已上架" value="12" />
                <el-option label="已下架" value="5" />
                <el-option label="待审核" value="10" />
                <el-option label="审核拒绝" value="11" />
                <el-option label="已删除" value="99" /> -->
                <el-option
                  v-for="item in statusEnumObj"
                  :key="item.code"
                  :label="item.text"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="帖子标题：">
              <el-input
                v-model="mainObj.searchForm.title"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="所属话题：">
              <el-select
                v-model="mainObj.searchForm.topicName"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in topicList"
                  :key="item.id"
                  :label="item.topicName"
                  :value="item.topicName"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="发帖人：">
              <el-input
                v-model="mainObj.searchForm.name"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="发帖时间：">
              <el-date-picker
                v-model="mainObj.searchForm.createTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择"
              />
            </el-form-item>
            <el-form-item label="置顶：">
              <el-select
                v-model="mainObj.searchForm.isTopicPostTop"
                placeholder="请选择"
                clearable
              >
                <el-option label="已置顶" :value="1" />
                <el-option label="未置顶" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="精华：">
              <el-select
                v-model="mainObj.searchForm.isEssence"
                placeholder="请选择"
                clearable
              >
                <el-option label="已加精" :value="1" />
                <el-option label="未加精" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="帖子来源：">
              <el-select
                v-model="mainObj.searchForm.postChannel"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部来源" value="" />
                <el-option label="市民卡平台" value="SK" />
                <el-option label="hello好店" value="HELLO" />
              </el-select>
            </el-form-item>

            <el-form-item label="帖子类型：">
              <el-select
                v-model="mainObj.searchForm.resourcesType"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部帖子" value="" />
                <el-option label="普通贴" value="1" />
                <el-option label="长文贴" value="2" />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
              <el-button
                type="primary"
                :icon="exportIcon"
                @click="exportPosts()"
              >导出</el-button>
            </el-form-item>
          </el-form>
          <!-- <el-button
            class="right"
            type="primary"
            @click="operation('add')"
          >新增</el-button> -->
        </div>
        <div class="table">
          <el-table
            ref="tableRef"
            :data="mainObj.list"
            fit
            border
            highlight-current-row
            size="small"
            :header-cell-style="{ background: '#F9F9F9' }"
          >
            <el-table-column prop="ipAddress" label="序号" width="50">
              <template slot-scope="scope">
                <span>{{ transTime(scope.$index) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="帖子ID" show-overflow-tooltip />
            <el-table-column prop="status" label="状态" />
            <!-- <el-table-column prop="status" label="状态">
              <template slot-scope="scope">
                <span>{{ transType(scope.row.status) }}</span>
              </template>
            </el-table-column> -->
            <!-- <el-table-column prop="classificationType" label="分类">
              <template slot-scope="scope">
                <span>{{ transType(scope.row.classificationType) }}</span>
              </template>
            </el-table-column> -->
            <el-table-column
              prop="title"
              label="帖子标题"
              width="200"
              show-overflow-tooltip
            />
            <el-table-column
              prop="title"
              label="帖子来源"
              width="200"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ scope.row.postChannel == 'HELLO'?'hello好店': scope.row.postChannel == 'SK'?'市民卡平台':'' }}
              </template>
            </el-table-column>
            <el-table-column
              prop="topicName"
              label="所属话题"
              width="200"
              show-overflow-tooltip
            />
            <el-table-column prop="likeNum" label="点赞数" />
            <el-table-column prop="commentNum" label="评论数" />
            <el-table-column prop="viewNum" label="浏览量" />
            <el-table-column prop="createUserName" label="发帖人" />
            <el-table-column prop="createTime" label="发帖时间" width="150" />
            <el-table-column prop="verifyUserName" label="审核人" />
            <el-table-column prop="verifyTime" label="审核时间" width="150" />
            <el-table-column prop="resourcesType" label="帖子类型" width="150">
              <template slot-scope="scope">
                {{ scope.row.resourcesType==='2'?'长文贴':"普通贴" }}
              </template>
            </el-table-column>
            <el-table-column prop="offShelfUser" label="下架人" width="150" />
            <el-table-column prop="offShelfTime" label="下架时间" width="150" />
            <el-table-column prop="deleteUser" label="删除人" width="150" />
            <el-table-column prop="deleteTime" label="删除时间" width="150" />
            <el-table-column label="操作" fixed="right" width="220">
              <template slot-scope="scope">
                <span>
                  <el-button
                    v-if="scope.row.status.code !== '11'"
                    type="text"
                    @click="operation('status', scope.row)"
                  >{{
                    scope.row.status.code === "5"
                      ? "上架"
                      : scope.row.status.code === "12"
                        ? "下架"
                        : ""
                  }}</el-button>
                  <el-button
                    v-if="
                      scope.row.status.code === '11' ||
                        scope.row.status.code === '10' ||
                        scope.row.status.code === '9'
                    "
                    type="text"
                    @click="operation('examine', scope.row)"
                  >
                    审核
                  </el-button>
                  <el-button
                    v-if="
                      scope.row.status.code === '12' ||
                        scope.row.status.code === '11' ||
                        scope.row.status.code === '5' ||
                        scope.row.status.code === '99' ||
                        scope.row.status.code === '9'
                    "
                    type="text"
                    @click="operation('view', scope.row)"
                  >
                    查看
                  </el-button>
                  <el-button
                    v-if="
                      scope.row.status.code === '11' ||
                        scope.row.status.code === '5'
                    "
                    type="text"
                    @click="operation('del', scope.row)"
                  >
                    删除
                  </el-button>
                  <el-button
                    v-if="scope.row.status.code === '12' && scope.row.isEssence ==='0'"
                    type="text"
                    @click="operation('pinned', scope.row)"
                  >
                    {{ scope.row.isTopicPostTop == 0? '置顶':'取消置顶' }}
                  </el-button>
                  <el-button
                    v-if="scope.row.status.code === '12' && scope.row.isTopicPostTop==='0'"
                    type="text"
                    @click="operation('refinement', scope.row)"
                  >
                    {{ scope.row.isEssence == 0? '加精':'取消加精' }}
                  </el-button>
                  <el-button
                    type="text"
                    @click="operation('getPostDetailApplys', scope.row)"
                  >
                    审核记录
                  </el-button>
                  <el-button v-if="scope.row.resourcesType==='2'" type="text" @click="edit(scope.row)">编辑</el-button>
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            :current-page="mainObj.currentPage"
            :page-size="mainObj.pageSize"
            :page-sizes="mainObj.pagesizes"
            :total="mainObj.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <detail ref="detail" :type="type" :row="row" @onsearch="onSearch" />
    <applyRecord ref="applyRecord" :row="row" />

    <articleEditing ref="articleEditing" @saveDialog="onSearch" />
  </div>
</template>

<script>
import sysManageApi from '@/api/sysManageApi'
import community from '@/api/community'
import checkPermission from '@/utils/permission'
import RouteTitle from '@/components/RouteTitle'
import detail from './detail'
import applyRecord from './applyRecord'
import { downOrViewFile, formatDates } from '@/utils/index'
import { Message } from 'element-ui'
import articleEditing from '../../community-manage/virtual-user/articleEditing'
import { getPostDetail } from '@/api/community'
const defaultSearchForm = {
  title: '',
  status: '',
  name: '',
  createTime: '',
  topicName: '',
  isTopicPostTop: '',
  postChannel: '',
  isEssence: '',
  resourcesType: ''
}

const defaultUserForm = {
  userName: '',
  mobile: '',
  roleIdList: []
}
export default {
  components: {
    RouteTitle,
    detail,
    applyRecord,
    articleEditing
  },
  data() {
    return {
      row: {},
      type: '',
      // token: getToken(),
      details: checkPermission(['requestLog_details']),

      infoId: '',
      dialogTableVisible: false,
      statusEnumObj: [],
      mainObj: {
        list: [],
        pageSize: 10,
        pagesizes: [10, 30, 50],
        currentPage: 1,
        total: 0,
        searchForm: { ...defaultSearchForm }
      },
      userDialogObj: {
        visible: false,
        title: '',
        type: '',
        form: Object.assign({}, defaultUserForm)
      },
      value: 1,
      topicList: [],
      postChannelList: [{ name: '市民卡平台', value: 'SK' }, { name: 'hello好店', value: 'HELLO' }],
      exportIcon: ''
    }
  },
  computed: {
    // transType() {
    //   return type => {
    //     return {
    //       10: '待审核',
    //       11: '审核拒绝',
    //       0: '待上架',
    //       12: '已上架',
    //       5: '已下架',
    //       99: '已删除'
    //     }[type]
    //   }
    // },
    transTime() {
      return index => {
        return (
          (this.mainObj.currentPage - 1) * this.mainObj.list.length + index + 1
        )
      }
    }
  },
  created() {
    this.getPostStatusEnum()
    this.onSearch()
    this.getTopicList()
  },
  methods: {
    exportPosts() {
      this.exportIcon = 'el-icon-loading'
      console.log(this.mainObj)
      const params = {
        postChannel: this.mainObj.searchForm.postChannel,
        status: this.mainObj.searchForm.status,
        title: this.mainObj.searchForm.title,
        name: this.mainObj.searchForm.name,
        topicName: this.mainObj.searchForm.topicName,
        createTime: this.mainObj.searchForm.createTime,
        isTopicPostTop: this.mainObj.searchForm.isTopicPostTop,
        isEssence: this.mainObj.searchForm.isEssence,
        resourcesType: this.mainObj.searchForm.resourcesType,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize
      }
      community
        .exportPostList(params)
        .then(res => {
          console.log(res)
          downOrViewFile(
            res.data,
            '帖子列表' + formatDates(new Date()) + '.xlsx'
          )
          this.exportIcon = ''
        })
        .catch(err => {
          console.log(err)
          Message({
            message: '无数据导出',
            type: 'error',
            duration: 5 * 1000
          })
          this.exportIcon = ''
        })
    },
    checkPermission,
    getPostStatusEnum() {
      sysManageApi.getTypeEnum({ enumName: 'PostStatusEnum' }).then(res => {
        this.statusEnumObj = res.data
      })
    },
    getTopicList() {
      community.searchAllTopicList().then(res => {
        this.topicList = [...res.data, { id: '', topicName: '全部' }]
      })
    },
    onSearch() {
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    onReset() {
      this.mainObj.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.mainObj.pageSize = val
      this.mainObj.currentPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.mainObj.currentPage = val
      this.fetchData()
    },
    getDetailInfo(row) {
      this.infoId = row.id.toString()
      this.dialogTableVisible = true
      if (this.$refs.child) {
        this.$refs.child.updatedInfo(row.id)
      }
    },
    fetchData() {
      const params = {
        postChannel: this.mainObj.searchForm.postChannel,
        status: this.mainObj.searchForm.status,
        title: this.mainObj.searchForm.title,
        name: this.mainObj.searchForm.name,
        topicName: this.mainObj.searchForm.topicName,
        createTime: this.mainObj.searchForm.createTime,
        pageNum: this.mainObj.currentPage,
        pageSize: this.mainObj.pageSize,
        isTopicPostTop: this.mainObj.searchForm.isTopicPostTop,
        isEssence: this.mainObj.searchForm.isEssence,
        resourcesType: this.mainObj.searchForm.resourcesType
      }
      community.getPostList(params).then(res => {
        this.mainObj.list = res.data.list
        this.mainObj.total = Number(res.data.total)
      })
    },
    operation(type, row = {}) {
      this.type = type
      this.row = row
      if (type === 'view' || type === 'examine') {
        this.$nextTick(() => {
          this.$refs.detail.dialogVisible = true
          this.$refs.detail.getDetail()
        })
      }
      if (type === 'status') {
        this.$confirm(
          `确定要将该帖子${row.status.code === '12' ? '下架' : '上架'}吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            const obj = {
              status: row.status.code,
              id: row.id
            }
            community.postChangeStatus(obj).then(res => {
              this.handleSizeChange()
              this.$message({
                type: 'success',
                message: obj.status === '12' ? '下架成功！' : '上架成功！'
              })
            })
          })
          .catch(() => {})
      }

      if (type === 'del') {
        this.$confirm(`确定要删除该帖子吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            community.deletePost([row.id]).then(res => {
              this.handleSizeChange()
              this.$message({
                type: 'success',
                message: '删除成功！'
              })
            })
          })
          .catch(() => {})
      }
      if (type === 'pinned' || type === 'refinement') {
        let btnTips = ''
        if (type === 'pinned') {
          btnTips = row.isTopicPostTop === '0' ? '置顶' : '取消置顶'
        } else if (type === 'refinement') {
          btnTips = row.isEssence === '0' ? '加精' : '取消加精'
        }
        this.$confirm(`请确认是否${btnTips}该条帖子？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            let status; let msg; let statusKey = ''
            if (type === 'pinned') {
              statusKey = 'isTop'
              status = row.isTopicPostTop === '0' ? '1' : '0'
              msg = row.isTopicPostTop === '0' ? '置顶成功' : '取消置顶成功'
            } else if (type === 'refinement') {
              statusKey = 'isEssense'
              status = row.isEssence === '0' ? '1' : '0'
              msg = row.isEssence === '0' ? '加精成功' : '取消加精成功'
            }

            community.updateTop({ postId: row.id, status, statusKey }).then(res => {
              this.handleSizeChange()
              this.$message({
                type: 'success',
                message: msg
              })
            })
          })
          .catch(() => {})
      }

      if (type === 'getPostDetailApplys') {
        this.$nextTick(() => {
          this.$refs.applyRecord.dialogVisible = true
          this.$refs.applyRecord.getPostDetailApplys()
        })
      }
    },
    edit(row) {
      getPostDetail({ postId: row.id }).then(res => {
        this.$refs.articleEditing.showDialo(res.data, 'update')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.right {
  float: right;
}

.left {
  float: left;
}

.body-top-form {
  overflow: hidden;
}
</style>
