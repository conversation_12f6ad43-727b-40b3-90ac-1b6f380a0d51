<template>
  <div>
    <RouteTitle />
    <!-- 条件查询 -->
    <div class="main-body">
      <div class="main-body-top">
        <!-- 页面条件搜索 -->
        <dynamic-query
          :data="queryConfig"
          @queryChanged="handleQueryDataChanged"
          @search="handleSearch"
        />
        <!-- 页面列表数据 + 分页条 -->
        <dynamic-table
          :config="tableConfig"
          :data="tableData"
          :is-loading.sync="tableLoading"
          @pagination="handlePageChange"
        >
          <!-- S 处理表格自定义插槽 -->
          <template #actStatusText="{ rowData, index }">
            {{ actStatusMap[rowData.actStatus] }}
          </template>
          <template #auditStatusText="{ rowData, index }">
            <span
              :style="{ color: rowData.auditStatus == 3 ? '#C03639' : '' }"
            >{{ auditStatusMap[rowData.auditStatus] }}
            </span>
          </template>
          <template #registerText="{ rowData, index }">
            {{ registerStatusMap[rowData.registerStatus] }}
          </template>
          <template v-slot:operate="{ rowData, index }">
            <el-button
              type="text"
              @click.stop="handleAddOrEdit('review', rowData)"
            >查看</el-button>
            <el-button
              v-if="rowData.auditStatus == 1"
              type="text"
              class="green-btn"
              @click.stop="handleReview(rowData.id, 2)"
            >审核通过</el-button>
            <el-button
              v-if="rowData.auditStatus == 1"
              type="text"
              class="red-btn"
              @click.stop="handleReview(rowData.id, 3)"
            >审核不通过</el-button>
          </template>
        </dynamic-table>
      </div>
    </div>
    <!-- 配置dialog -->
    <DialogForm
      v-if="dialogVisible"
      :dialog-visible="dialogVisible"
      :content-type-map="{}"
      :business-list="businessList"
      :config="dialogConfig"
      @handleClose="handleClose"
    />
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import DynamicQuery from '@/components/dynamic/Query.vue'
import DynamicTable from '@/components/dynamic/Table.vue'
import DialogForm from './components/dialogForm.vue'
import { actAudit, businessList } from '@/api/actManage'

const actStatusMap = {
  0: '下线',
  1: '上线'
}
const registerStatusMap = {
  0: '报名未开始',
  1: '报名进行中',
  2: '报名已结束'
}
const auditStatusMap = {
  0: '待提交审核',
  1: '审核中',
  2: '审核通过',
  3: '审核未通过'
}
import mixin from '../mixin'
export default {
  components: {
    RouteTitle,
    DynamicQuery,
    DynamicTable,
    DialogForm
  },
  mixins: [mixin],
  data() {
    return {
      auditPage: true,
      queryUrl: '/blindDate/listActInfo',
      dialogVisible: false,
      dialogConfig: {},
      queryConfig: {
        queryItem: [
          {
            type: 'select',
            label: '活动状态:',
            model: 'actStatus',
            optionValue: 'id',
            optionLabel: 'value',
            optionList: actStatusMap
          },
          {
            type: 'select',
            label: '报名状态:',
            model: 'registerStatus',
            optionValue: 'id',
            optionLabel: 'value',
            optionList: registerStatusMap
          },
          {
            type: 'select',
            label: '审核状态:',
            model: 'auditStatus',
            optionValue: 'id',
            optionLabel: 'value',
            optionList: auditStatusMap
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '活动ID:',
            model: 'id',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'text', // text文本框 textFilter文本框支持过滤 select下拉框 date日期/日期区间 option操作
            label: '活动名称:',
            model: 'actTitle',
            clearable: true, // 默认 true
            size: 'small' // 默认 small
          },
          {
            type: 'option'
          },
          {
            type: 'option',
            slotName: 'addBtn'
          }
        ]
      },
      // 列表字段
      tableConfig: {
        tableColumn: [
          {
            prop: '',
            label: '序号',
            isIndex: true
          },
          {
            prop: 'id',
            label: '活动ID'
          },
          {
            prop: 'actTitle',
            label: '活动名称',
            minWidth: 200
          },
          {
            prop: 'sort',
            label: '活动排序'
          },
          {
            prop: 'actStatus',
            label: '活动状态',
            slotName: 'actStatusText'
          },
          {
            prop: 'auditStatus',
            label: '审核状态',
            slotName: 'auditStatusText'
          },
          {
            prop: 'registerStatus',
            label: '报名状态',
            slotName: 'registerText'
          },
          {
            prop: 'numRage',
            label: '报名名额'
          },
          {
            prop: 'updateTime',
            label: '更新时间',
            width: 160
          },
          {
            prop: 'operator',
            label: '操作者'
          },
          {
            prop: '',
            label: '操作',
            slotName: 'operate', // 操作 - 自定义操作，和operateList二选一
            fixed: 'right',
            minWidth: 160
          }
        ]
      },

      // 列表数据
      tableData: {
        list: [],
        // 分页数据
        pageTotal: 0, // 列表总数
        pageSize: 10, // 每页条数
        pageNum: 1 // 当前页码
      },
      actStatusMap: actStatusMap,
      registerStatusMap: registerStatusMap,
      auditStatusMap: auditStatusMap
    }
  },
  mounted() {
    this.getBusinessList()
  },
  methods: {
    handleAddOrEdit(type, rowData) {
      this.dialogVisible = true
      this.dialogConfig = {
        title: '活动审核查看',
        formData: rowData,
        type
      }
    },
    // 提交审核
    handleReview(id, auditType) {
      const msg = auditType === 2 ? '确定审核通过吗？' : '确定审核不通过？'
      const btn = auditType === 2 ? '审核通过' : '审核不通过'
      this.$confirm(msg, '提示', {
        confirmButtonText: btn,
        cancelButtonText: '取消'
      })
        .then(() => {
          actAudit({ auditType, id }).then(res => {
            this.$message({
              type: 'success',
              message: res.msg
            })
            this.getList()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    // 获取商户列表
    getBusinessList() {
      businessList().then(res => {
        this.businessList = res.data.list
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.header-tab {
  display: inline-block;
  margin-left: 20px;
  font-weight: normal;
  font-size: 16px;
  .active {
    color: #1890ff;
    padding-bottom: 5px;
    border-bottom: 3px solid #1890ff;
  }
  .tab-line {
    margin: 0 10px;
  }
}
</style>
