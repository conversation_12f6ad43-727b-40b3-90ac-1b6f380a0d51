<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      top="4vh"
      width="820px"
      :title="config.title"
      :before-close="handleClose"
    >
      <table border="1" cellpadding="10" cellspacing="0" class="detail-table">
        <tbody>
          <tr>
            <td class="label">序号</td>
            <td>{{ config.index +1 }}</td>
          </tr>
          <tr v-for="(item,index) in config.formData.registerInfo" :key="index">
            <td class="label">{{ item.title }}</td>
            <td>{{ item.result }}</td>
          </tr>

        </tbody>
      </table>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">返 回</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'SignUpDialog',
  components: {},
  props: {
    dialogVisible: {
      type: <PERSON>ole<PERSON>,
      default: false
    },
    config: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {}
    }
  },
  created() {
    this.formData = {
      ...this.config.formData
    }
  },
  methods: {
    handleCancel() {
      this.$emit('cancel')
    },
    handleConfirm() {
      this.$emit('confirm')
    },
    handleClose() {
      this.$emit('handleClose')
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-table {
  width: 740px;
  margin: 0 auto;
  border-color: #DCDFE6;
  .label {
    width: 100px;
    text-align: center;
    font-weight: 700;
  }
}
</style>
