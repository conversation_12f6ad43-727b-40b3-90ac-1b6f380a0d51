import Mock from 'mockjs'

// 图标选项
const iconOptions = [
  { value: 'user', label: '用户' },
  { value: 'edit', label: '编辑' },
  { value: 'document', label: '文档' },
  { value: 'microphone', label: '麦克风' },
  { value: 'check', label: '检查' },
  { value: 'message', label: '消息' },
  { value: 'picture', label: '图片' },
  { value: 'upload', label: '上传' },
  { value: 'lock', label: '锁定' },
  { value: 'time', label: '时间' },
  { value: 'folder', label: '文件夹' },
  { value: 'menu', label: '菜单' },
  { value: 'plus', label: '加号' },
  { value: 'refresh', label: '刷新' },
  { value: 'video-camera', label: '摄像头' },
  { value: 'user-solid', label: '用户实心' },
  { value: 'delete', label: '删除' },
  { value: 'setting', label: '设置' },
  { value: 'star', label: '星星' },
  { value: 'star-on', label: '星星实心' },
  { value: 'house', label: '房子' },
  { value: 'search', label: '搜索' },
  { value: 'phone', label: '电话' },
  { value: 'notebook-1', label: '笔记本' },
  { value: 'goods', label: '商品' },
  { value: 'bell', label: '铃铛' },
  { value: 'pie-chart', label: '饼图' },
  { value: 'heart', label: '心形' },
  { value: 'thumb', label: '拇指' },
  { value: 'more', label: '更多' },
  { value: 'view', label: '查看' }
]

// 生成菜单数据
const menuList = []
let menuIdCounter = 1

// 生成目录
const directories = [
  { name: '系统管理', icon: 'setting', sort: 1 },
  { name: '活动管理', icon: 'house', sort: 2 },
  { name: '内容管理', icon: 'document', sort: 3 },
  { name: '用户管理', icon: 'user', sort: 4 }
]

directories.forEach(dir => {
  const dirMenu = {
    menuId: `dir_${menuIdCounter++}`,
    menuName: dir.name,
    parentId: null,
    type: 'directory', // 目录
    icon: dir.icon,
    sort: dir.sort,
    frontendUrl: '',
    backendUrl: '',
    description: `${dir.name}相关功能`,
    createTime: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
    updateTime: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
    children: []
  }
  
  // 为每个目录生成菜单
  const menus = [
    { name: '列表管理', frontendUrl: '/list', backendUrl: '/api/list', sort: 1 },
    { name: '权限管理', frontendUrl: '/permission', backendUrl: '/api/permission', sort: 2 }
  ]
  
  menus.forEach(menu => {
    const menuItem = {
      menuId: `menu_${menuIdCounter++}`,
      menuName: `${dir.name.replace('管理', '')}${menu.name}`,
      parentId: dirMenu.menuId,
      type: 'menu', // 菜单
      icon: '',
      sort: menu.sort,
      frontendUrl: menu.frontendUrl,
      backendUrl: menu.backendUrl,
      description: `${dir.name}下的${menu.name}`,
      createTime: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
      updateTime: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
      children: []
    }
    
    // 为每个菜单生成按钮
    const buttons = [
      { name: '查看', frontendUrl: '/view', backendUrl: '/api/view' },
      { name: '新增', frontendUrl: '/add', backendUrl: '/api/add' },
      { name: '修改', frontendUrl: '/edit', backendUrl: '/api/edit' },
      { name: '删除', frontendUrl: '/delete', backendUrl: '/api/delete' }
    ]
    
    buttons.forEach(btn => {
      const buttonItem = {
        menuId: `btn_${menuIdCounter++}`,
        menuName: btn.name,
        parentId: menuItem.menuId,
        type: 'button', // 按钮
        icon: '',
        sort: 0,
        frontendUrl: btn.frontendUrl,
        backendUrl: btn.backendUrl,
        description: `${menuItem.menuName}的${btn.name}操作`,
        createTime: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
        updateTime: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
        children: []
      }
      menuItem.children.push(buttonItem)
      menuList.push(buttonItem)
    })
    
    dirMenu.children.push(menuItem)
    menuList.push(menuItem)
  })
  
  menuList.push(dirMenu)
})

export default [
  // 获取菜单列表
  {
    url: '/dev-api/sys/menu/menuListQuery',
    type: 'post',
    response: config => {
      const { menuName, menuId } = config.body
      
      let filteredList = [...menuList]
      
      // 菜单名称模糊搜索
      if (menuName) {
        filteredList = filteredList.filter(item => 
          item.menuName.includes(menuName)
        )
      }
      
      // 菜单ID精确搜索
      if (menuId) {
        filteredList = filteredList.filter(item => item.menuId === menuId)
      }
      
      return {
        code: 200,
        message: 'success',
        data: {
          list: filteredList,
          total: filteredList.length
        }
      }
    }
  },

  // 新增菜单
  {
    url: '/dev-api/sys/menu/add',
    type: 'post',
    response: config => {
      const { menuName, parentId, type, icon, sort, frontendUrl, backendUrl, description } = config.body
      
      const newMenu = {
        menuId: `${type}_${Date.now()}`,
        menuName,
        parentId: parentId || null,
        type,
        icon: icon || '',
        sort: sort || 1,
        frontendUrl: frontendUrl || '',
        backendUrl: backendUrl || '',
        description: description || '',
        createTime: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
        updateTime: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
        children: []
      }
      
      menuList.push(newMenu)
      
      return {
        code: 200,
        message: '新增成功'
      }
    }
  },

  // 修改菜单
  {
    url: '/dev-api/sys/menu/modify',
    type: 'post',
    response: config => {
      const { menuId, menuName, parentId, type, icon, sort, frontendUrl, backendUrl, description } = config.body
      
      const menuIndex = menuList.findIndex(item => item.menuId === menuId)
      if (menuIndex === -1) {
        return {
          code: 400,
          message: '菜单不存在'
        }
      }
      
      const menu = menuList[menuIndex]
      menu.menuName = menuName
      menu.parentId = parentId || null
      menu.type = type
      menu.icon = icon || ''
      menu.sort = sort || 1
      menu.frontendUrl = frontendUrl || ''
      menu.backendUrl = backendUrl || ''
      menu.description = description || ''
      menu.updateTime = Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")')
      
      return {
        code: 200,
        message: '修改成功'
      }
    }
  },

  // 删除菜单
  {
    url: '/dev-api/sys/menu/delete',
    type: 'post',
    response: config => {
      const { menuId } = config.body
      
      const menuIndex = menuList.findIndex(item => item.menuId === menuId)
      if (menuIndex === -1) {
        return {
          code: 400,
          message: '菜单不存在'
        }
      }
      
      // 检查是否有子菜单
      const hasChildren = menuList.some(item => item.parentId === menuId)
      if (hasChildren) {
        return {
          code: 400,
          message: '该菜单下存在子菜单，无法删除'
        }
      }
      
      menuList.splice(menuIndex, 1)
      
      return {
        code: 200,
        message: '删除成功'
      }
    }
  },

  // 获取菜单详情
  {
    url: '/dev-api/sys/menu/getMenuDetail',
    type: 'get',
    response: config => {
      const { menuId } = config.query
      
      const menu = menuList.find(item => item.menuId === menuId)
      if (!menu) {
        return {
          code: 400,
          message: '菜单不存在'
        }
      }
      
      return {
        code: 200,
        message: 'success',
        data: menu
      }
    }
  },

  // 获取图标选项
  {
    url: '/dev-api/sys/menu/getIconOptions',
    type: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: iconOptions
      }
    }
  }
]
