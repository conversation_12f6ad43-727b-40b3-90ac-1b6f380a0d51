<template>
  <p class="tips" :style="{'color': txtColor}">
    {{ txt }}
    <el-tooltip v-if="showMore" popper-class="tips-item" effect="light" placement="right">
      <div slot="content">
        <p>
          1. 不可在图片中出现文字、水印、logo、遮挡等内容（尤其第三方网站水印）
        </p>
        <p>2. 不可使用海报、拼接图片，不得含有边框、底纹</p>
      </div>
      <span>更多规范</span>
    </el-tooltip>
  </p>
</template>

<script>
export default {
  props: {
    txt: {
      type: String,
      default: ''
    },
    txtColor: {
      type: String,
      default: ''
    },
    showMore: { type: Boolean, default: false }
  }
}
</script>

<style lang="scss" scoped>
.tips {
  margin: 0;
  color: #c0c4cc;
  font-size: 12px;
  span {
    color: #ee604c;
  }
}
</style>
