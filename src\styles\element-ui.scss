// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 500 !important;
  color: rgba(0, 0, 0, 0.4);
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  .el-dialog__footer {
    background-color: #fff;
    padding: 10px 20px;
    text-align: right;
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-tag + .el-tag {
  margin-left: 10px;
}

.el-dialog {
  border-radius: 6px;
  overflow: hidden;
}

.el-input.is-disabled .el-input__inner {
  background-color: #f8f8f8;
}

.el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  /*height:600px;*/
  max-height:94vh;
}

.el-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
}

.el-dialog__body::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  }
  /*正常情况下滑块的样式*/
  .el-dialog__body::-webkit-scrollbar-thumb {
  background-color: rgba(0,0,0,.05);
  border-radius: 10px;
  }
  /*鼠标悬浮在该类指向的控件上时滑块的样式*/
  .el-dialog__body:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0,0,0,.2);
  border-radius: 10px;
  }
  /*鼠标悬浮在滑块上时滑块的样式*/
  .el-dialog__body::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0,0,0,.4);
  }
  /*正常时候的主干部分*/
  .el-dialog__body::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: white;
  }
  /*鼠标悬浮在滚动条上的主干部分*/
  .el-dialog__body::-webkit-scrollbar-track:hover {
    background-color: rgba(0,0,0,.01);
  }

.el-dialog__wrapper{
  display: flex;
  justify-content: center;
  align-items: center;
  width:100%;
}


.uplaodto .el-upload {
  background-color: #f8f8f8 !important;
  cursor: not-allowed !important;
  &:hover {
    border: 1px dashed #d9d9d9 !important;
  }
}

.el-textarea.is-disabled .el-textarea__inner{
  background-color: #f8f8f8 !important;
}

.el-range-editor.is-disabled{
  background-color: #f8f8f8 !important;
}

.el-dialog .el-dialog__footer{
  padding:15px 20px;
}

/* 设置滚动条宽度和高度 */
.el-table__body-wrapper::-webkit-scrollbar {
  width: 8px; /* 横向滚动条 */
  height: 8px; /* 纵向滚动条 必写 */
}
/* 设置滚动条样式 */
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}

.el-table__fixed, .el-table__fixed-right{
  height: calc(100% - 10px) !important;
}
.el-table__fixed-body-wrapper {
  height:100% !important;
}

.el-table__fixed-body-wrapper .el-table__body {
  /*滚动条高度*/
  padding-bottom: 50px !important;
}
