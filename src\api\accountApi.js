import request from '@/utils/request'

// 获取账号列表
export function getAccountList(data) {
  return request({
    url: '/sys/account/search',
    method: 'post',
    data
  })
}

// 新增账号
export function addAccount(data) {
  return request({
    url: '/sys/account/add',
    method: 'post',
    data
  })
}

// 修改账号
export function modifyAccount(data) {
  return request({
    url: '/sys/account/modify',
    method: 'post',
    data
  })
}

// 删除账号
export function deleteAccount(data) {
  return request({
    url: '/sys/account/delete',
    method: 'post',
    data
  })
}

// 获取角色列表
export function getRoleList() {
  return request({
    url: '/sys/account/roles',
    method: 'get'
  })
}

export default {
  getAccountList,
  addAccount,
  modifyAccount,
  deleteAccount,
  getRoleList
}
