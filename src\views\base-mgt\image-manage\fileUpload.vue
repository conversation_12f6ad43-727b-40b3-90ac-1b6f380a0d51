<template>
  <div class="container">
    <div class="label">支持：jpg、jpeg、bmp、png的格式。附件大小不超过1M。</div>
    <div class="upload-box">
      <div v-if="fileVos.file.url" class="upload">
        <img
          :src=" fileVos.file.url "
          class="el-upload-list__item-thumbnail"
        >
        <span class="el-upload-list__item-actions">
          <span
            class="el-upload-list__item-preview"
            @click="handlePictureCardPreview(fileVos.file)"
          >
            <i class="el-icon-zoom-in" />
          </span>
          <span
            v-if="type != 'view'"
            class="el-upload-list__item-delete"
            @click="handleRemove(fileVos)"
          >
            <i class="el-icon-delete" />
          </span>
        </span>
      </div>
      <el-upload
        v-else
        ref="upload"
        :action="url"
        :on-success="(res, file, fileList) => { return handleSuccess(res, file, fileList)}"
        :before-upload="(file) => {return beforeUpload(file)}"
        name="file"
        :headers="{'X-Auth-Token' : token}"
        :show-file-list="false"
        :disabled="type == 'view'"
      >
        <i
          class="el-icon-plus"
        />
      </el-upload>
      <!-- <div ref="tips" class="tips"><span style="color: red;margin-right: 5px;font-size: 18px;line-height: 15px;">*</span>{{ fileVos.fileTypeName }}</div> -->
    </div>
    <div style="clear:both" />
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'

export default {
  components: {},
  props: {
    fileVos: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      token: getToken(),
      dialogImageUrl: '',
      dialogVisible: false,
      url: process.env.VUE_APP_BASE_API + '/file/upload/file'
    }
  },
  watch: {
  },
  created() {

  },
  mounted() {
  },

  methods: {

    /**
         * @method: 限制上传文件格式和大小
         */
    beforeUpload(file) {
      const isJPEG = file.type === 'image/jpeg'
      const isJPG = file.type === 'image/jpg'
      const isPNG = file.type === 'image/png'
      // const isGIF = file.type === 'image/gif'
      const isBMP = file.type === 'image/bmp'
      // const isLt5M = (file.size / 1024 / 1024 )< 2
      if (!isJPEG && !isJPG && !isPNG && !isBMP) {
        this.$message.error('上传文件只能是 JPG, JPEG, PNG, BMP 格式!')
        this.clearFile()
        return false
      }
      if (file.size > 1 * 1024 * 1024) {
        this.$message.error('上传文件大小不能超过 1MB!')
        this.clearFile()
        return false
      }
    },
    clearFile() {
      // var files = this.fileVos
      // files.url = ''
      // files.fileId = ''
      // files.fileName = ''
      // files.filePath = ''
      // files.filePathStr = ''
      // files.fileFormat = ''
      // this.fileVos = files
      this.fileVos.file = {}
    },
    handleRemove(file) {
      this.clearFile()
    },
    handlePictureCardPreview(file) {
      this.$emit('previewFile', file)
    },
    handleSuccess(res, file, fileList) {
      console.log(res)
      if (res.code === 200) {
        const data = JSON.parse(JSON.stringify(res.data))
        this.fileVos.file = data
        this.fileVos.imageFileId = data.id
        this.fileVos.url = data.url
        this.fileVos.imageName = file.name
      } else {
        this.$message.error('上传失败')
      }
    },
    check() {
      // this.$refs.tips.style.color = 'red'
      this.$nextTick(() => {
        this.$refs.upload.$children[0].$el.style.border = '1px solid red'
        this.$message({
          type: 'error',
          message: '请上传图片'
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>

 .container {
    width: 100%;
     margin-bottom: 8px;
    // background: #F7F9FB;
    border-radius: 2px;
    padding: 20px;
  }
  .label {
    padding: 0 0 5px;
    line-height: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #000000;
    // margin-top: 27px;
  }
  /deep/.upload-box {
    float: left;
    height: 180px;
    width: 190px;
    margin-right: 10px;
  }
  /deep/.el-upload {
    text-align: center;
    cursor: pointer;
    outline: 0;
    background-color: #fbfdff;
    border: 1px dashed #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 148px;
    height: 148px;
    line-height: 146px;
    vertical-align: top;
    margin-bottom: 8px;
    i {
      font-size: 28px;
      color: #8c939d;
    }
  }
  /deep/.upload {
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #D6DCDF;
    border-radius: 6px;
    box-sizing: border-box;
    width: 148px;
    height: 148px;
    margin-bottom: 8px;
    display: inline-block;
    color: #606266;
    line-height: 1.8;
    position: relative;
    .el-upload-list__item-thumbnail {
      width: 100%;
      height: 100%;
    }
    .el-upload-list__item-actions {
      position: absolute;
      width: 100%;
      height: 100%;
      line-height: 146px;
      left: 0;
      top: 0;
      cursor: default;
      text-align: center;
      color: #fff;
      opacity: 0;
      font-size: 20px;
      background-color: rgba(0,0,0,.5);
      transition: opacity .3s;
      span {
        display: none;
        cursor: pointer;
      }
      span+span {
        margin-left: 15px;
      }
      .el-upload-list__item-delete {
        right: 10px;
        top: 0;
        display: none;
        position: static;
        font-size: inherit;
        color: inherit;
      }
      ::after {
        display: inline-block;
        content: "";
        height: 100%;
        vertical-align: middle;
      }
      &:hover {
        opacity: 1;
        span {
          display: inline-block;
        }
      }
    }
  }
  .tips {
    width: 148px;
    text-align: center;
    font-size: 12px;
    font-weight: 500;
    color: #000000;
  }
</style>
