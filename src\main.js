import Vue from 'vue'
// import Vconsole from 'vconsole'
import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'

import VueCropper from 'vue-cropper'

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log

import * as filters from './filters' // global filters
import '@/utils/directives' // global directives
import 'default-passive-events'
// const console = new Vconsole()
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (process.env.NODE_ENV === 'production') {
  const { mockXHR } = require('../mock')
  mockXHR()
}

Vue.use(VueCropper)

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})
// 弹出框的时候滚动条隐藏和出现导致页面抖动问题
Element.Dialog.props.lockScroll.default = false
Element.Dialog.props.closeOnPressEscape.default = false
// 修改 el-dialog 默认点击遮照不关闭
Element.Dialog.props.closeOnClickModal.default = false
// 确认框设置全局默认值
Vue.prototype.$confirm = function() {
  const params1to2 = [].slice.call(arguments, 0, 2)
  const params3 = [].slice.call(arguments, 2).map(e => {
    e.closeOnClickModal = e.closeOnClickModal ? e.closeOnClickModal : false
    return e
  })
  return Element.MessageBox.confirm(...params1to2, ...params3)
}

// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
