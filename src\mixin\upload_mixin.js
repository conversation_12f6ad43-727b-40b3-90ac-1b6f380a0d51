import community from '@/api/community'
export default {
  data() {
    return {
      imgView: false,
      fileList: [],
      imageUrl: '',
      coverImgId: ''
    }
  },
  methods: {
    /**
        *@method: 限制上传文件格式和大小
        */
    beforeUpload(file) {
      const isJPEG = file.type === 'image/jpeg'
      const isJPG = file.type === 'image/jpg'
      const isPNG = file.type === 'image/png'
      // const isGIF = file.type === 'image/gif'
      const isBMP = file.type === 'image/bmp'
      if (!isJPEG && !isJPG && !isPNG && !isBMP) {
        this.$message.error('上传文件只能是 JPG, JPEG, PNG, BMP 格式!')
        return false
      }
      if (file.size > 2 * 1024 * 1024) {
        this.$message.error('上传文件大小不能超过 2MB!')
        return false
      }
    },
    handleRemove() {
      this.imageUrl = ''
      this.coverImgId = ''
      this.fileList = []
    },
    handleChange() {
      this.fileList = []
    },
    uploadImage(file) {
      const formData = new FormData()
      formData.append('file', file.file)
      community.uploadFile(formData).then(res => {
        this.imageUrl = res.data.url
        this.coverImgId = res.data.id
      })
    },

    handlePictureCardPreview() {
      this.imgView = true
    }
  }

}
