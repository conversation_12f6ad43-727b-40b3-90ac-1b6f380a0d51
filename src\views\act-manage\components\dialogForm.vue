<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      top="4vh"
      :before-close="handleClose"
    >
      <div slot="title" class="dialog-act-title">
        {{ config.title }}
        <span :class="{ active: steps == 1 }">【1】活动基本信息 </span>
        <i class="el-icon-right" />
        <span :class="{ active: steps == 2 }">【2】活动报名信息</span>
        <el-button
          v-if="steps == 1"
          type="primary"
          @click="steps = 2"
        >继续下一步</el-button>
        <el-button
          v-if="steps == 2"
          type="primary"
          @click="steps = 1"
        >返回上一步</el-button>
      </div>
      <el-form
        ref="ruleForm"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="demo-ruleForm"
        label-position="right"
        size="small"
        :disabled="config.type == 'review'"
      >
        <div v-show="steps == 1">
          <el-form-item label="商户名称：" prop="merId">
            <el-select v-model="formData.merId" placeholder="请选择">
              <el-option
                v-for="(item, index) in businessList"
                :key="index"
                :label="item.merName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <div class="flex-box">
            <el-form-item label="活动名称：" prop="actTitle">
              <el-input
                v-model="formData.actTitle"
                placeholder="请输入"
                minlength="8"
                maxlength="28"
              />
              <Tips txt="限8-28字" />
            </el-form-item>
            <el-form-item label="活动排序：" prop="sort">
              <el-input v-model.number="formData.sort" placeholder="请输入" />
              <Tips txt="请填入数字1-99，数字越小排序越靠前" />
            </el-form-item>
          </div>
          <div class="flex-box">
            <el-form-item label="活动开始时间：" prop="actStartTime">
              <el-date-picker
                v-model="formData.actStartTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width:280px"
                type="datetime"
                placeholder="请输入活动开始时间"
              />
            </el-form-item>
            <el-form-item label="活动结束时间：" prop="actEndTime">
              <el-date-picker
                v-model="formData.actEndTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width:280px"
                type="datetime"
                placeholder="请输入活动结束时间"
              />
            </el-form-item>
          </div>
          <div class="flex-box">
            <el-form-item label="报名开始时间：" prop="registerStartTime">
              <el-date-picker
                v-model="formData.registerStartTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width:280px"
                type="datetime"
                placeholder="请输入报名开始时间"
              />
            </el-form-item>
            <el-form-item label="报名结束时间：" prop="registerEndTime">
              <el-date-picker
                v-model="formData.registerEndTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width:280px"
                type="datetime"
                placeholder="请输入报名结束时间"
              />
            </el-form-item>
          </div>
          <el-form-item label="活动地点：" prop="location">
            <el-input v-model="formData.location" placeholder="请输入" />
            <Tips
              txt="活动举办地点，即场馆名称、楼宇名称、园区名称、社区名称等。示例：遇见博物馆·杭州馆"
            />
          </el-form-item>
          <el-form-item label="详细地址：" prop="localDetail">
            <el-input v-model="formData.localDetail" placeholder="请输入" />
            <Tips
              txt="地点详细地址，精确至：市区路门牌号楼层。示例：杭州市滨江区滨盛路3867号宝龙城5F"
            />
          </el-form-item>
          <el-form-item label="活动描述：" prop="actDesc">
            <Editor
              ref="editor"
              :flag="config.type == 'review'"
              :editor-html.sync="formData.actDesc"
              file-type="kb"
              :file-size="500"
            />
            <Tips
              :show-more="true"
              txt="限200-5000字符，图片大小小于500kb，不超过10张"
            />
          </el-form-item>
          <el-form-item label="活动图片：" prop="imgFirst">
            <ImgUpload
              v-model="formData.imgFirst"
              :disabled="config.type == 'review'"
              :img-size="{
                contrast: '>=',
                imgWidth: 210,
                imgHeight: 210
              }"
              file-type="kb"
              img-txt="添加小封面"
              :file-size="500"
            >
              <template #tips>
                <Tips
                  :show-more="true"
                  txt="尺寸: >210x210像素，大小: ≤500kb，限1张，用于在活动列表中展示"
                />
              </template>
            </ImgUpload>
            <el-input
              v-show="false"
              v-model="formData.imgFirst"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="" prop="imgSecond">
            <ImgUpload
              v-model="formData.imgSecond"
              img-txt="添加大封面"
              :disabled="config.type == 'review'"
              :img-size="{
                contrast: '>=',
                imgWidth: 750,
                imgHeight: 420
              }"
              file-type="kb"
              :file-size="500"
            >
              <template #tips>
                <Tips
                  :show-more="true"
                  txt="尺寸: >750x420像素，大小: ≤500kb，限1张，用于在热门活动，推荐位，或活动详情页顶部等位置进行展示。应尽量避免与活动描述中的图片重复"
                />
              </template>
            </ImgUpload>
            <el-input
              v-show="false"
              v-model="formData.imgSecond"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="分享内容：" prop="actShare">
            <el-input
              v-model="formData.actShare"
              type="textarea"
              maxlength="50"
              minlength="5"
              placeholder="请输入"
            />
            <Tips
              txt="限5-50字，作为微信分享链接中的内容（微信分享链接由缩略图、标题、内容组成）"
            />
          </el-form-item>
          <el-form-item label="主办方名称：" prop="sponsorName">
            <el-input v-model="formData.sponsorName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="咨询联系：" prop="sponsorTel">
            <el-input v-model="formData.sponsorTel" placeholder="请输入" />
            <Tips
              txt="手机号码、固话、微信号等。示例：都市红娘小杨，13737690101（微信同号）"
            />
          </el-form-item>
          <el-form-item label="活动服务：" prop="actServe">
            <el-input v-model="formData.actServe" placeholder="请输入" />
            <Tips txt="如免费停车，免费WIFI，大巴接送等" />
          </el-form-item>
          <el-form-item label="其他说明：" prop="actDetail">
            <el-input v-model="formData.actDetail" placeholder="请输入" />
            <Tips txt="需告知用户的其他说明" />
          </el-form-item>
        </div>
        <div v-show="steps == 2">
          <el-form-item label="报名信息：" prop="registerInfo">
            <Tips
              txt-color="#ec808d"
              txt="用户报名时，需填写的参与用户信息。请勿勾选不必要的信息"
            />
            <el-checkbox-group v-model="formData.registerInfo">
              <ul class="register-box">
                <li v-for="(item, index) in defaultRegister" :key="index">
                  <div
                    v-for="obj in item"
                    :key="obj.key"
                    :style="{ 'justify-content': !obj.key ? 'center' : '' }"
                    class="info-box"
                  >
                    <el-checkbox v-if="obj.key" :label="obj.key">{{
                      obj.title
                    }}</el-checkbox>
                    <span v-else>{{ obj.title }}</span>
                  </div>
                </li>
              </ul>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="用户限制：" prop="registerUserRole">
            <Tips
              txt-color="#ec808d"
              txt="用户报名时，对用户身份进行校验。不符合的无法提交报名信息"
            />
            <el-radio-group v-model="formData.registerUserRole" class="radio-select">
              <div><el-radio label="0">无限制</el-radio></div>
              <div>
                <el-radio label="1">仅限单身用户</el-radio>
              </div>
              <div>
                <el-radio label="2">仅限家长用户</el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="性别限制：" prop="registerSex">
            <Tips
              txt-color="#ec808d"
              txt="用户报名时，通过身份证号码进行校验。不符合的无法提交报名信息"
            />
            <el-radio-group v-model="formData.registerSex" class="radio-select">
              <div><el-radio label="0">无限制</el-radio></div>
              <div>
                <el-radio label="1">仅限男</el-radio>
              </div>
              <div>
                <el-radio label="2">仅限女</el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="ageBox" label="年龄限制：" prop="registerAge">
            <Tips
              txt-color="#ec808d"
              txt="用户报名时，通过身份证号码进行校验。不符合的无法提交报名信息"
            />
            <el-radio-group v-model="formData.registerAge" class="radio-select">
              <div><el-radio label="0">无限制</el-radio></div>
              <div style="display: flex; align-items: center;">
                <el-radio label="1">建议</el-radio>
                <template v-if="formData.registerAge == 1">
                  <el-form-item label="" style="margin: 0;" prop="startAge">
                    <el-input
                      v-model.number="formData.startAge"
                      style="width:116px;"
                      placeholder="输入年龄数字"
                    />
                  </el-form-item>
                  &nbsp;&nbsp; 到 &nbsp;&nbsp;
                  <el-form-item style="margin: 0;" label="" prop="endAge">
                    <el-input
                      v-model.number="formData.endAge"
                      style="width:116px;"
                      placeholder="输入年龄数字"
                    />
                  </el-form-item>
                  周岁的用户报名参加
                </template>
              </div>
              <div style="display: flex; align-items: center;">
                <el-radio label="2">强制</el-radio>
                <template v-if="formData.registerAge == 2">
                  <el-form-item label="" style="margin: 0;" prop="startAge">
                    <el-input
                      v-model.number="formData.startAge"
                      style="width:116px;"
                      placeholder="输入年龄数字"
                    />
                  </el-form-item>
                  &nbsp;&nbsp; 到 &nbsp;&nbsp;
                  <el-form-item style="margin: 0;" label="" prop="endAge">
                    <el-input
                      v-model.number="formData.endAge"
                      style="width:116px;"
                      placeholder="输入年龄数字"
                    />
                  </el-form-item>
                  周岁的用户报名参加
                </template>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="婚姻限制：" prop="registerMarry">
            <Tips
              txt-color="#ec808d"
              txt="用户报名时，通过数管、民政等相关数据进行校验。不符合的无法提交报名信息"
            />
            <el-radio-group
              v-model="formData.registerMarry"
              class="radio-select"
            >
              <div><el-radio label="0">无限制</el-radio></div>
              <div>
                <el-radio
                  label="1"
                >强制仅限单身（含未婚、离异、丧偶等无配偶婚姻状态）</el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="人数限制：" prop="ifLimit">
            <Tips txt-color="#ec808d" txt="" />
            <el-radio-group v-model="formData.ifLimit" class="radio-select">
              <div><el-radio :label="false">无限制</el-radio></div>
              <div style="display: flex; align-items: center;">
                <el-radio style="margin:0;" :label="true">仅限：</el-radio>
                <template v-if="formData.ifLimit">
                  <el-form-item style="margin: 0;" label="" prop="numRage">
                    <el-input
                      v-model="formData.numRage"
                      style="width:116px;"
                      placeholder="输入数字"
                    />
                    人报名
                  </el-form-item>
                </template>
              </div>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          v-if="config.type == 'review'"
          @click="handleClose"
        >关 闭</el-button>
        <template v-else>
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleSave">确 定</el-button>
        </template>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ImgUpload from '@/components/ImgUpload'
import Editor from '@/components/Editor'
import Tips from './tips.vue'
import validators from '@/utils/validate'
import { defaultRegister } from './register'
import { actAddOrEdit } from '@/api/actManage'
export default {
  name: 'DialogForm',
  components: {
    ImgUpload,
    Editor,
    Tips
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => ({})
    },
    businessList: {
      type: Array,
      default: () => []
    },
    homePageType: {
      type: Number,
      default: 1
    },
    contentTypeMap: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const validateHtml = (rule, value, callback) => {
      if (value === '' || value === '<p><br></p>') {
        callback(new Error('请输入活动描述'))
      } else if (this.$refs.editor.editor.getText().length > 5000) {
        callback(new Error('活动描述最多可输入5000字'))
      } else if (this.$refs.editor.editor.getText().length < 200) {
        callback(new Error('活动描述最少输入200字'))
      } else {
        callback()
      }
    }
    return {
      steps: 1,
      formData: {
        contentType: '',
        contentImg: '',
        registerInfo: ['name', 'mobile', 'idCard']
      },
      defaultRegister: defaultRegister,
      rules: {
        merId: [validators.required('商户名称', 'change')],
        actTitle: [
          validators.required('活动名称'),
          {
            pattern: /^.{8,28}$/,
            message: '文本限8-28字',
            trigger: 'blur'
          }
        ],
        actStartTime: [validators.required('活动开始时间', 'change')],
        actEndTime: [validators.required('活动结束时间', 'change')],
        registerStartTime: [validators.required('报名开始时间', 'change')],
        registerEndTime: [validators.required('报名结束时间', 'change')],
        location: [validators.required('活动地点')],
        localDetail: [validators.required('详细地址')],
        actDesc: [
          validators.required('活动描述', 'change'),
          {
            validator: validateHtml,
            trigger: 'change'
          }
        ],
        sort: [
          validators.required('排序', 'blur', { type: 'number' }),
          {
            pattern: /\b[1-9]\d?\b/,
            message: '请输入数字1-99',
            trigger: 'blur'
          }
        ],
        imgFirst: [validators.required('小封面图', 'change')],
        imgSecond: [validators.required('大封面图', 'change')],
        actShare: [
          validators.required('分享内容'),
          {
            pattern: /^.{5,50}$/,
            message: '限5-50字',
            trigger: 'blur'
          }
        ],
        sponsorName: [validators.required('主办方名称')],
        sponsorTel: [validators.required('咨询联系')],
        registerInfo: [
          validators.required('报名信息', 'change', { type: 'array' })
        ],
        registerUserRole: [
          validators.required('用户限制', 'change')
        ],
        registerSex: [
          validators.required('性别限制', 'change')
        ],
        registerAge: [validators.required('年龄限制', 'change')],
        startAge: [validators.required('', 'blur', { type: 'number' })],
        endAge: [validators.required('', 'blur', { type: 'number' })],
        registerMarry: [validators.required('婚姻限制', 'change')],
        ifLimit: [
          validators.required('人数限制', 'change', { type: 'boolean' })
        ]
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const type = this.config.type
      if (type === 'edit' || type === 'review') {
        let registerArr = JSON.parse(this.config.formData.registerInfo)
        console.log(registerArr)
        registerArr = registerArr.map(item => {
          return item.key
        })
        this.formData = {
          ...this.config.formData
        }
        this.formData.registerInfo = registerArr
      }
    },
    handleSave() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          const params = {
            ...this.formData
          }
          if (!params.ifLimit) {
            params.numRage = '0'
          }
          const url = this.config.url
          actAddOrEdit(params, url).then(res => {
            this.$message.success(res.msg)
            this.$emit('saveSuccess')
          })
        } else {
          return false
        }
      })
    },
    handleCancel() {
      this.$emit('cancel')
    },
    handleConfirm() {
      this.$emit('confirm')
    },
    handleClose() {
      this.$emit('handleClose')
      this.$refs['ruleForm'].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  width: 940px;
  height: 92vh;
  .el-input {
    width: 300px;
  }
  .radio-select {
    div {
      font-size: 14px;
      label {
        height: 32px;
        line-height: 32px;
      }
    }
  }
}
.dialog-act-title {
  line-height: 20px;
  button {
    float: right;
    margin-right: 30px;
  }
  span {
    display: inline-block;
    margin: 0 10px;
    font-size: 14px;
  }
  .active {
    color: #1890ff;
  }
}
.register-box {
  padding: 0;
  border-bottom: 1px solid #ddd;
  border-right: 1px solid #ddd;
  li {
    display: flex;
  }
  .info-box {
    display: flex;
    align-items: center;
    width: 128px;
    padding: 0 5px;
    border: 1px solid #ddd;
    border-bottom: none;
    border-right: none;
    font-size: 12px;
    ::v-deep .el-checkbox__label {
      padding-left: 5px;
    }
  }
}
</style>
