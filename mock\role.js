import Mock from 'mockjs'

// 模拟角色列表数据
const roleList = []
for (let i = 1; i <= 50; i++) {
  roleList.push(Mock.mock({
    roleId: 1000 + i,
    roleName: '@pick(["系统管理员", "普通用户", "审核员", "运营人员", "客服人员"])' + i,
    createTime: '@datetime("yyyy-MM-dd HH:mm:ss")',
    creator: 'admin',
    updateTime: '@datetime("yyyy-MM-dd HH:mm:ss")',
    updater: 'admin',
    remark: '@csentence(10, 100)',
    permissions: ['@integer(1, 100)', '@integer(1, 100)', '@integer(1, 100)']
  }))
}

// 模拟权限树数据
const permissionTree = [
  {
    id: 1,
    name: '系统管理',
    children: [
      {
        id: 11,
        name: '用户管理',
        children: [
          { id: 111, name: '查看用户' },
          { id: 112, name: '新增用户' },
          { id: 113, name: '修改用户' },
          { id: 114, name: '删除用户' }
        ]
      },
      {
        id: 12,
        name: '角色管理',
        children: [
          { id: 121, name: '查看角色' },
          { id: 122, name: '新增角色' },
          { id: 123, name: '修改角色' },
          { id: 124, name: '删除角色' }
        ]
      }
    ]
  },
  {
    id: 2,
    name: '业务管理',
    children: [
      {
        id: 21,
        name: '活动管理',
        children: [
          { id: 211, name: '查看活动' },
          { id: 212, name: '新增活动' },
          { id: 213, name: '修改活动' },
          { id: 214, name: '删除活动' }
        ]
      }
    ]
  }
]

export default [
  // 获取角色列表
  {
    url: '/dev-api/sys/role/search',
    type: 'post',
    response: config => {
      const { roleName, pageNum = 1, pageSize = 10 } = config.body

      let filteredList = roleList

      // 角色名称模糊搜索
      if (roleName) {
        filteredList = filteredList.filter(item =>
          item.roleName.toLowerCase().includes(roleName.toLowerCase())
        )
      }

      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + parseInt(pageSize)
      const pageData = filteredList.slice(start, end)

      return {
        code: 200,
        message: 'success',
        data: {
          list: pageData.map(item => ({
            ...item,
            id: item.roleId,
            updateTime: item.updateTime,
            roleType: { code: '3' } // 模拟角色类型
          })),
          total: filteredList.length
        }
      }
    }
  },

  // 新增角色
  {
    url: '/dev-api/sys/role/add',
    type: 'post',
    response: config => {
      const { roleName, roleRemark, permIdList } = config.body
      const newRole = {
        roleId: roleList.length + 1001,
        roleName,
        createTime: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
        creator: 'admin',
        updateTime: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
        updater: 'admin',
        remark: roleRemark,
        permissions: permIdList
      }
      roleList.unshift(newRole)

      return {
        code: 200,
        msg: '新增成功'
      }
    }
  },

  // 修改角色
  {
    url: '/dev-api/sys/role/modify',
    type: 'post',
    response: config => {
      const { roleId, roleName, roleRemark, permIdList } = config.body
      const roleIndex = roleList.findIndex(item => item.roleId === roleId)
      if (roleIndex !== -1) {
        roleList[roleIndex].roleName = roleName
        roleList[roleIndex].remark = roleRemark
        roleList[roleIndex].permissions = permIdList
        roleList[roleIndex].updateTime = Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")')
      }

      return {
        code: 200,
        msg: '修改成功'
      }
    }
  },

  // 删除角色
  {
    url: '/dev-api/sys/role/delete',
    type: 'post',
    response: config => {
      const { roleId } = config.body
      const roleIndex = roleList.findIndex(item => item.roleId === roleId)
      if (roleIndex !== -1) {
        roleList.splice(roleIndex, 1)
      }

      return {
        code: 200,
        msg: '删除成功'
      }
    }
  },

  // 获取角色详情
  {
    url: '/dev-api/sys/role/getRoleDetail',
    type: 'get',
    response: config => {
      const { roleId } = config.query
      const role = roleList.find(item => item.roleId.toString() === roleId.toString())

      if (role) {
        return {
          code: 200,
          message: 'success',
          data: {
            id: role.roleId,
            roleName: role.roleName,
            roleCode: 'ROLE_' + role.roleName,
            roleStatus: '1',
            roleStatusName: '启用',
            roleType: '3',
            permDetailDtoList: permissionTree,
            permissions: role.permissions || []
          }
        }
      }

      return {
        code: 400,
        message: '角色不存在'
      }
    }
  },

  // 获取权限树
  {
    url: '/dev-api/sys/role/getPermTree',
    type: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: permissionTree.map(item => ({
          ...item,
          permName: item.name,
          subPermDetailDtoList: item.children ? item.children.map(child => ({
            ...child,
            permName: child.name,
            subPermDetailDtoList: child.children ? child.children.map(grandChild => ({
              ...grandChild,
              permName: grandChild.name,
              subPermDetailDtoList: []
            })) : []
          })) : []
        }))
      }
    }
  },
  
  // 检查角色关联
  {
    url: '/dev-api/system/role/[0-9]+/association',
    type: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: {
          hasAssociation: Mock.mock('@boolean')
        }
      }
    }
  }
]