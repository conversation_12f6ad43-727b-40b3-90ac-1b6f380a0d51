import Mock from 'mockjs'

// 模拟角色列表数据
const roleList = []
for (let i = 1; i <= 50; i++) {
  roleList.push(Mock.mock({
    roleId: 1000 + i,
    roleName: '@pick(["系统管理员", "普通用户", "审核员", "运营人员", "客服人员"])' + i,
    createTime: '@datetime("yyyy-MM-dd HH:mm:ss")',
    creator: 'admin',
    updateTime: '@datetime("yyyy-MM-dd HH:mm:ss")',
    updater: 'admin',
    remark: '@csentence(10, 100)',
    permissions: ['@integer(1, 100)', '@integer(1, 100)', '@integer(1, 100)']
  }))
}

// 模拟权限树数据
const permissionTree = [
  {
    id: 1,
    name: '系统管理',
    children: [
      {
        id: 11,
        name: '用户管理',
        children: [
          { id: 111, name: '查看用户' },
          { id: 112, name: '新增用户' },
          { id: 113, name: '修改用户' },
          { id: 114, name: '删除用户' }
        ]
      },
      {
        id: 12,
        name: '角色管理',
        children: [
          { id: 121, name: '查看角色' },
          { id: 122, name: '新增角色' },
          { id: 123, name: '修改角色' },
          { id: 124, name: '删除角色' }
        ]
      }
    ]
  },
  {
    id: 2,
    name: '业务管理',
    children: [
      {
        id: 21,
        name: '活动管理',
        children: [
          { id: 211, name: '查看活动' },
          { id: 212, name: '新增活动' },
          { id: 213, name: '修改活动' },
          { id: 214, name: '删除活动' }
        ]
      }
    ]
  }
]

export default [
  // 获取角色列表
  {
    url: '/dev-api/system/role/list',
    type: 'get',
    response: config => {
      const { roleId, roleName, pageNum = 1, pageSize = 10 } = config.query
      
      let filteredList = roleList
      
      // 角色ID精确搜索
      if (roleId) {
        filteredList = filteredList.filter(item => item.roleId.toString() === roleId)
      }
      
      // 角色名称模糊搜索
      if (roleName) {
        filteredList = filteredList.filter(item => 
          item.roleName.toLowerCase().includes(roleName.toLowerCase())
        )
      }
      
      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + parseInt(pageSize)
      const pageData = filteredList.slice(start, end)
      
      return {
        code: 200,
        message: 'success',
        data: {
          list: pageData,
          total: filteredList.length
        }
      }
    }
  },
  
  // 创建角色
  {
    url: '/dev-api/system/role',
    type: 'post',
    response: () => {
      return {
        code: 200,
        message: '创建成功',
        data: {
          roleId: Mock.mock('@integer(10000, 99999)')
        }
      }
    }
  },
  
  // 更新角色
  {
    url: '/dev-api/system/role/[0-9]+',
    type: 'put',
    response: () => {
      return {
        code: 200,
        message: '更新成功',
        data: null
      }
    }
  },
  
  // 删除角色
  {
    url: '/dev-api/system/role/[0-9]+',
    type: 'delete',
    response: () => {
      return {
        code: 200,
        message: '删除成功',
        data: null
      }
    }
  },
  
  // 获取权限树
  {
    url: '/dev-api/system/permission/tree',
    type: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: permissionTree
      }
    }
  },
  
  // 检查角色关联
  {
    url: '/dev-api/system/role/[0-9]+/association',
    type: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: {
          hasAssociation: Mock.mock('@boolean')
        }
      }
    }
  }
]