import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 300000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    if (store.getters.token) {
      const { url } = config
      if (url === '/post/exportPostList' || url === '/communityUser/exportUser' || url === '/postCommentary/exportCommentaryList' || url === '/topic/exportPrize') {
        config['responseType'] = 'blob'
        config.headers['Content-Type'] = 'application/json; charset=UTF-8'
      }
      config.headers['X-Auth-Token'] = getToken()
    }
    return config
  },
  error => {
    console.log(error, 'error')
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  response => {
    // const res = response.data
    const { responseType } = response.config
    if (responseType === 'blob' || responseType === 'arraybuffer') {
      if (!response.config.isToJson || response.data.size > 1000) {
        return response
      }
      const reader = new FileReader()
      reader.readAsText(response.data)
      return new Promise((resolve, reject) => {
        reader.onload = function() {
          try {
            const result = JSON.parse(reader.result)
            if (result.data && result.data.code !== 200) {
              Message({
                message: result.data.msg,
                type: 'error',
                duration: 5 * 1000
              })
              reject(new Error(result.data.msg || 'Error'))
            } else {
              resolve(response)
            }
          } catch (e) {
            resolve(response)
          }
        }
      }).then(res => {
        return res
      })
    }

    if (!response) {
      return response
    }
    // const { data } = response.data
    const res = response.data
    if (res.code !== 200) {
      Message({
        message: res.msg || 'Error',
        type: 'error',
        duration: 5 * 1000
      })

      if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
        MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {
          confirmButtonText: 'Re-Login',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      }
      return Promise.reject(new Error(res.msg || 'Error'))
    } else {
      const token = response.headers['x-auth-token']
      if (token) {
        res.token = token
      }
      return res
    }
  },
  async error => {
    try {
      const { status } = error.response
      if (status && status === 401) {
        await store.dispatch('user/logout')
        Message({
          message: '登录过期',
          type: 'error',
          duration: 5 * 1000
        })
      } else if (error.response.config.responseType === 'blob') {
        return
      } else {
        Message({
          message: error,
          type: 'error',
          duration: 5 * 1000
        })
      }
    } catch (e) {
      Message({
        message: error,
        type: 'error',
        duration: 5 * 1000
      })
    }
    return Promise.reject(error.message)
  }
)

export default service
