<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-top" style="padding-bottom: 20px;">
        <el-form :inline="true" :model="dataForm">
          <el-form-item label="菜单名：">
            <el-input
              v-model="dataForm.menuName"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="菜单Id：">
            <el-input
              v-model="dataForm.menuId"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getDataList()">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="addOrUpdateHandle('add')"
            >新增</el-button>
          </el-form-item>
        </el-form>

        <el-table
          :data="dataList"
          row-key="menuId"
          border
          style="width: 100%; "
        >
          <el-table-column
            prop="menuName"
            header-align="center"
            min-width="150"
            label="名称"
          />
          <el-table-column
            prop="sort"
            header-align="center"
            align="center"
            label="排序号"
          />
          <el-table-column
            prop="menuId"
            header-align="center"
            align="center"
            width="150"
            label="菜单URL"
          />
          <el-table-column
            header-align="center"
            align="center"
            width="150"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="addOrUpdateHandle('edit', scope.row.menuId)"
              >修改</el-button>
              <el-button
                type="text"
                size="small"
                @click="deleteHandle(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      :tree-data="dataList"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import AddOrUpdate from '../components/menu-add-or-update.vue'
import { treeDataTranslate } from '@/utils'

import { getMenuList, delMenu } from '@/api/sysManageApi'
export default {
  components: {
    RouteTitle,
    AddOrUpdate
  },
  data() {
    return {
      dataForm: {},
      dataList: [],
      dataListLoading: false,
      addOrUpdateVisible: false
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      const params = {
        ...this.dataForm,
        pageSize: 999
      }
      getMenuList(params)
        .then(res => {
          let arr = res.data.list
          arr = arr.filter(item => item.menuId !== 'root')
          this.dataList = treeDataTranslate(arr, 'menuId')
        })
        .finally(() => {
          this.dataListLoading = false
        })
    },
    // 新增 / 修改
    addOrUpdateHandle(type, menuId) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(type, menuId)
      })
    },
    // 删除
    deleteHandle(row) {
      this.$confirm(`确定对${row.menuName}进行[删除]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          delMenu({ menuId: row.menuId }).then(res => {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          })
        })
        .catch(() => {})
    },
    handlePageChange: function(obj) {
      this.$emit('pagination', obj)
    }
  }
}
</script>
