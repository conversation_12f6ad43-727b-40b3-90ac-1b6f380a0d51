import Mock from 'mockjs'

// 生成操作日志数据
const operationLogList = []
const count = 100

// 操作类型
const operationTypes = [
  '用户登录',
  '用户登出',
  '新增账号',
  '修改账号',
  '删除账号',
  '新增角色',
  '修改角色',
  '删除角色',
  '新增菜单',
  '修改菜单',
  '删除菜单',
  '查询用户列表',
  '查询角色列表',
  '查询菜单列表',
  '上传文件',
  '下载文件',
  '导出数据',
  '修改密码',
  '重置密码',
  '权限分配'
]

// 用户账号列表
const userAccounts = [
  'admin',
  'zhangsan',
  'lisi',
  'wangwu',
  'zhaoliu',
  'qianqi',
  'sunba',
  'zhoujiu',
  'wushi'
]

// 用户姓名列表
const userNames = [
  '管理员',
  '张三',
  '李四',
  '王五',
  '赵六',
  '钱七',
  '孙八',
  '周九',
  '吴十'
]

for (let i = 0; i < count; i++) {
  const userIndex = Mock.mock('@integer(0, 8)')
  const operationType = Mock.mock(`@pick(${JSON.stringify(operationTypes)})`)
  
  operationLogList.push(Mock.mock({
    id: '@increment',
    operationContent: operationType,
    requestParams: function() {
      // 根据操作类型生成不同的请求参数
      switch (operationType) {
        case '新增账号':
          return JSON.stringify({
            userName: '@cname',
            account: '@word(6, 12)',
            phone: /^1[3-9]\d{9}$/,
            roleId: '@integer(1, 4)'
          })
        case '修改账号':
          return JSON.stringify({
            id: '@integer(1, 100)',
            userName: '@cname',
            phone: /^1[3-9]\d{9}$/,
            status: '@integer(1, 2)'
          })
        case '删除账号':
          return JSON.stringify({
            id: '@integer(1, 100)'
          })
        case '查询用户列表':
          return JSON.stringify({
            userName: '@cname',
            pageNum: '@integer(1, 10)',
            pageSize: '@integer(10, 50)'
          })
        case '用户登录':
          return JSON.stringify({
            account: userAccounts[userIndex],
            password: '******'
          })
        case '权限分配':
          return JSON.stringify({
            roleId: '@integer(1, 10)',
            permissionIds: ['@integer(1, 50)', '@integer(1, 50)', '@integer(1, 50)']
          })
        default:
          return JSON.stringify({
            action: operationType,
            timestamp: '@datetime("yyyy-MM-dd HH:mm:ss")'
          })
      }
    },
    createTime: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
    operator: userAccounts[userIndex],
    operatorName: userNames[userIndex],
    ipAddress: Mock.mock('@ip'),
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  }))
}

// 按创建时间倒序排列
operationLogList.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))

export default [
  // 获取操作日志列表
  {
    url: '/dev-api/request-log/list',
    type: 'post',
    response: config => {
      const { userName, account, pageNum = 1, pageSize = 10 } = config.body
      
      let filteredList = operationLogList
      
      // 用户姓名模糊搜索
      if (userName) {
        filteredList = filteredList.filter(item => 
          item.operatorName.includes(userName)
        )
      }
      
      // 账号精确搜索
      if (account) {
        filteredList = filteredList.filter(item => item.operator === account)
      }
      
      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + parseInt(pageSize)
      const pageData = filteredList.slice(start, end)
      
      return {
        code: 200,
        message: 'success',
        data: {
          list: pageData,
          total: filteredList.length
        }
      }
    }
  },

  // 获取操作日志详情
  {
    url: '/dev-api/request-log/detail',
    type: 'get',
    response: config => {
      const { id } = config.query
      
      const log = operationLogList.find(item => item.id.toString() === id.toString())
      if (!log) {
        return {
          code: 400,
          message: '日志不存在'
        }
      }
      
      return {
        code: 200,
        message: 'success',
        data: log
      }
    }
  }
]
