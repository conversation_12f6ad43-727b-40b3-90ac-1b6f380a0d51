<template>
  <div style="border: 1px solid #ccc">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editor"
      :default-config="toolbarConfig"
      :mode="mode"
    />
    <Editor
      v-model="html"
      style="height: 500px; overflow-y: hidden"
      :default-config="editorConfig"
      :mode="mode"
      @onCreated="onCreated"
      @onChange="onChange"
    />
  </div>
</template>

<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { uploadFile } from '@/api/community'

export default {
  name: 'EditorHtml',
  components: { Editor, Toolbar },
  props: {
    editorHtml: {
      type: String,
      default: ''
    },
    fileSize: {
      type: Number,
      default: 2
    },
    fileType: {
      type: String,
      default: 'MB'
    },
    maxNumFile: {
      type: Number,
      default: 10
    },
    flag: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editor: null,
      html: this.editorHtml,
      toolbarConfig: {},
      editorConfig: {
        placeholder: '请输入内容...',
        MENU_CONF: {
          uploadImage: {}
        }
      },
      mode: 'default' // or 'simple'
    }
  },
  watch: {
    editorHtml() {
      this.html = this.editorHtml
    }
  },
  created() {
    this.initImgUpload()
  },
  mounted() {
    // 模拟 ajax 请求，异步渲染编辑器
    setTimeout(() => {
      this.html = this.editorHtml
    }, 1500)
  },
  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁编辑器
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
      if (this.flag) {
        this.editor.disable()
      }
    },
    onChange(editor) {
      this.html = editor.isEmpty() ? '' : this.html
      this.$emit('update:editorHtml', this.html)
    },
    // 上传
    initImgUpload() {
      const _this = this

      this.editorConfig.MENU_CONF['uploadImage'] = {
        fieldName: 'file',
        // 最多可上传几个文件，默认为 10
        maxNumberOfFiles: _this.maxNumFile,
        async customUpload(file, insertFn) {
          let isLt
          if (_this.fileType == 'MB') {
            isLt = file.size / 1024 / 1024 < _this.fileSize
          } else {
            isLt = file.size / 1024 < _this.fileSize
          }
          if (!isLt) {
            // 判断大小
            _this.$message.error(
              `上传图片的大小不能超过 ${_this.fileSize}${_this.fileType}!`
            )
            return
          }
          const formData = new FormData()
          formData.append('file', file)
          uploadFile(formData)
            .then(res => {
              insertFn(res.data.url)
            })
            .catch(() => {})
            .finally(() => {})
        }
      }
    }

  }
}
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
<style lang="scss" scoped>
::v-deep h5,
.h5 {
  font-size: 14px;
}

::v-deep h4,
.h4 {
  font-size: 16px;
  font-weight: bold;
}

::v-deep h3,
.h3 {
  font-size: 18px;
  font-weight: bold;
}

::v-deep h2,
.h2 {
  font-size: 20px;
  font-weight: bold;
}

::v-deep h1,
.h1 {
  font-size: 22px;
  font-weight: bold;
}
::v-deep i,
em {
  font-style: italic;
}
::v-deep .w-e-toolbar .w-e-menu i {
  font-style: normal;
}
::v-deep ol {
  list-style-type: decimal;
}
</style>
