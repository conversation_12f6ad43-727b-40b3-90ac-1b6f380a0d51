import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/auth-redirect',
    component: () => import('@/views/login/auth-redirect'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: {
          title: '首页', icon: 'dashboard', affix: true,
          icon_o: require('@/assets/menu/home_s.svg'),
          icon_c: require('@/assets/menu/home_n.svg')
        }
      }
    ]
  },
  {
    path: '/profile',
    component: Layout,
    redirect: '/profile/index',
    hidden: true,
    children: [
      {
        path: 'index',
        component: () => import('@/views/profile/index'),
        name: 'profile',
        meta: { title: '修改密码' }

      }
    ]
  }

]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  {
    path: '/community-page',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    meta: {
      title: '社区首页',
      icon_o: require('@/assets/menu/system_s.svg'),
      icon_c: require('@/assets/menu/system_n.svg'),
      iconShow: true
    },
    children: [
      {
        path: '/community-page/index',
        component: () => import('@/views/community-page/index'),
        name: 'CommunityPage',
        meta: {
          title: '首页配置',
          // roles: ['communityPage'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      }
    ]
  },
  // 系统管理
  {
    path: '/sys',
    component: Layout,
    alwaysShow: true,
    meta: {
      title: '系统管理',
      roles: ['sysMgt'],
      icon_o: require('@/assets/menu/system_s.svg'),
      icon_c: require('@/assets/menu/system_n.svg'),
      iconShow: true
    },
    children: [
      {
        path: '/sys/user-manage/index',
        component: () => import('@/views/system-setting/user-manage/index'),
        name: 'UserManage',
        meta: {
          title: '用户管理',
          roles: ['sysUser'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/sys/role-manage/index',
        component: () => import('@/views/system-setting/role-permission-manage/index'),
        name: 'RoleManage',
        meta: {
          title: '角色管理',
          roles: ['sysRole'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/sys/exhibition-booth/index',
        component: () => import('@/views/system-setting/exhibition-booth/index'),
        name: 'exhibitionBooth',
        meta: {
          title: '展台管理',
          roles: ['glBooth'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/sys/menu-manage/index',
        component: () => import('@/views/system-setting/menu-manage/index'),
        name: 'MenuManage',
        meta: {
          title: '菜单管理',
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      }
    ]
  },
  // 活动管理
  {
    path: '/act-manage',
    component: Layout,
    redirect: '/act-mange/act-list',
    alwaysShow: true,
    meta: {
      title: '活动管理',
      // roles: ['communityPage'],
      icon_o: require('@/assets/menu/system_s.svg'),
      icon_c: require('@/assets/menu/system_n.svg'),
      iconShow: true
    },
    children: [
      {
        path: '/act-mange/act-list',
        component: () => import('@/views/act-manage/actList'),
        name: 'ActList',
        meta: {
          title: '活动列表',
          // roles: ['communityPage'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/act-mange/act-review',
        component: () => import('@/views/act-manage/actReview'),
        name: 'ActReview',
        meta: {
          title: '活动审核',
          // roles: ['communityPage'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/act-mange/sign-up',
        component: () => import('@/views/act-manage/signUpList'),
        name: 'SignUpList',
        meta: {
          title: '报名列表',
          // roles: ['communityPage'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/act-mange/business-list',
        component: () => import('@/views/act-manage/businessList'),
        name: 'BusinessList',
        meta: {
          title: '商户信息',
          // roles: ['communityPage'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      }
    ]
  },
  // 话题管理
  {
    path: '/topic-manage',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    meta: {
      title: '话题管理',
      roles: ['topicMgt'],
      icon_o: require('@/assets/menu/system_s.svg'),
      icon_c: require('@/assets/menu/system_n.svg'),
      iconShow: true
    },
    children: [
      {
        path: '/topic-manage/index',
        component: () => import('@/views/topic-manage/topiclist'),
        name: 'Topiclist',
        meta: {
          title: '话题列表',
          roles: ['topic1111'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      }
    ]
  },
  // 内容管理
  {
    path: '/commentary-manage',
    component: Layout,
    redirect: '/commentary-manage/index',
    alwaysShow: true,
    meta: {
      title: '内容管理',
      roles: ['contentMgt'],
      icon_o: require('@/assets/menu/system_s.svg'),
      icon_c: require('@/assets/menu/system_n.svg'),
      iconShow: true
    },
    children: [
      {
        path: '/post-manage/index',
        component: () => import('@/views/post-manage/postsList'),
        name: 'postsList',
        meta: {
          title: '帖子管理',
          roles: ['post'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/commentary-manage/index',
        component: () => import('@/views/commentary-manage'),
        name: 'commentary-manage',
        meta: {
          title: '评论管理',
          roles: ['commentary'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/commentary-mange/interest-tags',
        component: () => import('@/views/commentary-manage/interestTags'),
        name: 'InterestTags',
        meta: {
          title: '兴趣标签库',
          // roles: ['communityPage'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/commentary-mange/problem-base',
        component: () => import('@/views/commentary-manage/problemBase'),
        name: 'ProblemBase',
        meta: {
          title: '互聊问题库',
          // roles: ['communityPage'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/commentary-mange/img-example',
        component: () => import('@/views/commentary-manage/imgExample'),
        name: 'ImgExample',
        meta: {
          title: '相册机审表',
          // roles: ['communityPage'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      }
    ]
  },
  // 社区管理
  {
    path: '/community-manage',
    component: Layout,
    redirect: '/community-manage/community-user/index',
    alwaysShow: true,
    meta: {
      title: '社区管理',
      roles: ['communityMgt'],
      icon_o: require('@/assets/menu/system_s.svg'),
      icon_c: require('@/assets/menu/system_n.svg'),
      iconShow: true
    },
    children: [
      {
        path: '/community-manage/community-user/index',
        component: () => import('@/views/community-manage/community-user'),
        name: 'communityUser',
        meta: {
          title: '社区用户',
          roles: ['communityUser'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/community-manage/virtual-user/index',
        component: () => import('@/views/community-manage/virtual-user'),
        name: 'virtualUser',
        meta: {
          title: '马甲管理',
          roles: ['virtualUser'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/community-manage/user-info/index',
        component: () => import('@/views/community-manage/user-info'),
        name: 'UserInfo',
        meta: {
          title: '用户管理',
          // roles: ['virtualUser'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/community-manage/log-off/index',
        component: () => import('@/views/community-manage/log-off'),
        name: 'virtualUser',
        meta: {
          title: '注销审核',
          // roles: ['virtualUser'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      }
    ]
  },
  // 数据统计
  {
    path: '/data-statistics',
    component: Layout,
    redirect: '/data-statistics/user-statistics/index',
    alwaysShow: true,
    meta: {
      title: '数据统计',
      roles: ['statistics'],
      icon_o: require('@/assets/menu/system_s.svg'),
      icon_c: require('@/assets/menu/system_n.svg'),
      iconShow: true
    },
    children: [
      {
        path: '/data-statistics/user-statistics/index',
        component: () => import('@/views/data-statistics/user-statistics'),
        name: 'userStatistics',
        meta: {
          title: '用户规模统计',
          roles: ['searchUserStatisticsList'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/data-statistics/topic-statistics/index',
        component: () => import('@/views/data-statistics/topic-statistics'),
        name: 'topicStatistics',
        meta: {
          title: '话题统计',
          roles: ['searchTopicReleaseStatisticsList', 'searchOpTopicList', 'searchTopicStatisticsList', 'searchTopicUserStatisticsList'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
		{
		  path: '/data-statistics/hongniang/index',
		  component: () => import('@/views/data-statistics/hongniang'),
		  name: 'hniangStatistics',
		  meta: {
			title: '红娘网数据同步',
			roles: ['hniangStatisticsList'],
			icon_o: require('@/assets/menu/Group_s.svg'),
			icon_c: require('@/assets/menu/Group_n.svg')
		  }
		},
    ]
  },

  {
    path: '/base-mgt',
    component: Layout,
    alwaysShow: true,
    meta: {
      title: '基础功能',
      roles: ['baseMgt'],
      icon_o: require('@/assets/menu/system_s.svg'),
      icon_c: require('@/assets/menu/system_n.svg'),
      iconShow: true
    },
    children: [
      {
        path: '/base-mgt/config-manage/index',
        component: () => import('@/views/base-mgt/config-manage/index'),
        name: 'ConfigManage',
        meta: {
          title: '参数管理',
          roles: ['glConfig'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/base-mgt/image-manage/index',
        component: () => import('@/views/base-mgt/image-manage/index'),
        name: 'ImageManage',
        meta: {
          title: '图片管理',
          roles: ['glImage'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/base-mgt/dict-manage/index',
        component: () => import('@/views/base-mgt/dict-manage/index'),
        name: 'DictManage',
        meta: {
          title: '字典管理',
          roles: ['glDict'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      },
      {
        path: '/base-mgt/prize-manage/index',
        component: () => import('@/views/base-mgt/prize-manage/index'),
        name: 'PrizeManage',
        meta: {
          title: '奖品管理',
          // roles: ['glDict'],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      }
    ]
  },
  {
    path: '/log-manage',
    component: Layout,
    redirect: '/log-manage/operation-log/index',
    alwaysShow: true,
    meta: {
      title: '日志管理',
      roles: [''],
      icon_o: require('@/assets/menu/journal_s.svg'),
      icon_c: require('@/assets/menu/journal_n.svg'),
      iconShow: true
    },
    children: [
      {
        path: '/log-manage/operation-log/index',
        component: () => import('@/views/log-manage/operation-log/index'),
        name: 'operationManagement',
        meta: {
          title: '操作日志',
          roles: [''],
          icon_o: require('@/assets/menu/Group_s.svg'),
          icon_c: require('@/assets/menu/Group_n.svg')
        }
      }
    ]
  },
  // {
  //   path: '/file-manage',
  //   component: Layout,
  //   redirect: '/file-manage/index',
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/file-manage/index'),
  //       name: 'FileManage',
  //       meta: {
  //         title: '文件管理'
  //       }
  //     }
  //   ]
  // },
  // 404 page must be placed at the end !!!

  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
