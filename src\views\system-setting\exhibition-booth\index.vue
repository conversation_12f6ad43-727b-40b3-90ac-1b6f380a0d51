<template>
  <div>
    <RouteTitle />
    <div class="main-body">
      <div class="main-body-bottom">
        <!-- 查询表单 -->
        <div class="body-top-form">
          <el-form
            ref="searchForm"
            :model="searchForm"
            label-width="80px"
            label-position="left"
            class="left"
            inline
          >
            <el-form-item label="用户姓名">
              <el-input
                v-model.trim="searchForm.userName"
                placeholder="请输入用户姓名"
                clearable
                style="width: 200px;"
              />
            </el-form-item>
            <el-form-item label="账号">
              <el-input
                v-model.trim="searchForm.account"
                placeholder="请输入账号"
                clearable
                style="width: 200px;"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 数据表格 -->
        <div class="table">
          <el-table
            ref="tableRef"
            :data="tableData"
            fit
            border
            highlight-current-row
            :header-cell-style="{'background': '#F9F9F9'}"
          >
            <el-table-column
              type="index"
              label="序号"
              width="60"
              :index="getTableIndex"
            />
            <el-table-column
              prop="operationContent"
              label="操作内容"
              min-width="150"
              show-overflow-tooltip
            />
            <el-table-column
              prop="requestParams"
              label="请求参数"
              min-width="200"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <div class="params-cell">
                  {{ scope.row.requestParams }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="createTime"
              label="创建时间"
              width="160"
              sortable
            />
            <el-table-column
              prop="operator"
              label="操作人"
              width="120"
            />
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            :current-page="currentPage"
            :page-size="pageSize"
            :page-sizes="pageSizes"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import operationLogApi from '@/api/operationLogApi'
import checkPermission from '@/utils/permission'
import RouteTitle from '@/components/RouteTitle'

const defaultSearchForm = {
  userName: '',
  account: ''
}

export default {
  components: {
    RouteTitle
  },
  data() {
    return {
      isloading: false,
      searchForm: { ...defaultSearchForm },
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      pageSizes: [10, 30, 50],
      total: 0
    }
  },
  created() {
    this.onSearch()
  },
  methods: {
    checkPermission,

    onSearch() {
      this.currentPage = 1
      this.fetchData()
    },

    onReset() {
      this.searchForm = { ...defaultSearchForm }
      this.fetchData()
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchData()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },

    // 获取表格序号
    getTableIndex(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1
    },

    // 获取数据
    fetchData() {
      const params = {
        userName: this.searchForm.userName,
        account: this.searchForm.account,
        pageNum: this.currentPage,
        pageSize: this.pageSize
      }

      operationLogApi.getOperationLogList(params).then((res) => {
        this.tableData = res.data.list
        this.total = res.data.total
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body-bottom {
  box-sizing: border-box;
  padding: 20px;
}

.body-top-form {
  overflow: hidden;
  margin-bottom: 20px;
}

.right {
  float: right;
}

.left {
  float: left;
}

.params-cell {
  max-width: 200px;
  word-break: break-all;
  white-space: pre-wrap;
}

.table {
  margin-bottom: 20px;
}

.pagination {
  text-align: right;
}
</style>
