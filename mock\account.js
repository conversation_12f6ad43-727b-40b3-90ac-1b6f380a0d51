import Mock from 'mockjs'

// 生成账号列表数据
const accountList = []
const count = 50

for (let i = 0; i < count; i++) {
  accountList.push(Mock.mock({
    id: '@increment',
    account: '@word(4, 16)',
    userName: '@cname',
    phone: /^1[3-9]\d{9}$/,
    'roleId|1': [1, 2, 3, 4],
    roleName: function() {
      const roles = ['管理员', '活动员', '审核员', '内容员']
      return roles[this.roleId - 1]
    },
    'status|1': [1, 2], // 1-正常 2-禁用
    statusName: function() {
      return this.status === 1 ? '正常' : '禁用'
    },
    description: '@cparagraph(1, 3)',
    createTime: '@datetime("yyyy-MM-dd HH:mm:ss")',
    createBy: 'admin',
    updateTime: '@datetime("yyyy-MM-dd HH:mm:ss")',
    updateBy: 'admin'
  }))
}

// 角色列表数据
const roleList = [
  { id: 1, roleName: '管理员' },
  { id: 2, roleName: '活动员' },
  { id: 3, roleName: '审核员' },
  { id: 4, roleName: '内容员' }
]

export default [
  // 获取账号列表
  {
    url: '/dev-api/sys/account/search',
    type: 'post',
    response: config => {
      const { userName, account, roleId, status, createTimeStart, createTimeEnd, pageNum = 1, pageSize = 10 } = config.body
      
      let filteredList = accountList
      
      // 用户姓名模糊搜索
      if (userName) {
        filteredList = filteredList.filter(item => 
          item.userName.includes(userName)
        )
      }
      
      // 账号精确搜索
      if (account) {
        filteredList = filteredList.filter(item => item.account === account)
      }
      
      // 角色筛选
      if (roleId) {
        filteredList = filteredList.filter(item => item.roleId.toString() === roleId.toString())
      }
      
      // 状态筛选
      if (status) {
        filteredList = filteredList.filter(item => item.status.toString() === status.toString())
      }
      
      // 创建时间筛选
      if (createTimeStart && createTimeEnd) {
        filteredList = filteredList.filter(item => {
          const itemTime = new Date(item.createTime).getTime()
          const startTime = new Date(createTimeStart).getTime()
          const endTime = new Date(createTimeEnd).getTime()
          return itemTime >= startTime && itemTime <= endTime
        })
      }
      
      // 按创建时间倒序排列
      filteredList.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
      
      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + parseInt(pageSize)
      const pageData = filteredList.slice(start, end)
      
      return {
        code: 200,
        message: 'success',
        data: {
          list: pageData,
          total: filteredList.length
        }
      }
    }
  },

  // 新增账号
  {
    url: '/dev-api/sys/account/add',
    type: 'post',
    response: config => {
      const { account, userName, phone, roleId, password, status, description } = config.body
      
      // 检查账号是否已存在
      const existAccount = accountList.find(item => item.account === account)
      if (existAccount) {
        return {
          code: 400,
          message: '账号已存在'
        }
      }
      
      const newAccount = {
        id: accountList.length + 1,
        account,
        userName,
        phone,
        roleId,
        roleName: roleList.find(r => r.id === roleId)?.roleName || '',
        status,
        statusName: status === 1 ? '正常' : '禁用',
        description: description || '',
        createTime: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
        createBy: 'admin',
        updateTime: Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")'),
        updateBy: 'admin'
      }
      
      accountList.unshift(newAccount)
      
      return {
        code: 200,
        message: '新增成功'
      }
    }
  },

  // 修改账号
  {
    url: '/dev-api/sys/account/modify',
    type: 'post',
    response: config => {
      const { id, userName, phone, roleId, password, status, description } = config.body
      
      const accountIndex = accountList.findIndex(item => item.id === id)
      if (accountIndex === -1) {
        return {
          code: 400,
          message: '账号不存在'
        }
      }
      
      const account = accountList[accountIndex]
      account.userName = userName
      account.phone = phone
      account.roleId = roleId
      account.roleName = roleList.find(r => r.id === roleId)?.roleName || ''
      account.status = status
      account.statusName = status === 1 ? '正常' : '禁用'
      account.description = description || ''
      account.updateTime = Mock.mock('@datetime("yyyy-MM-dd HH:mm:ss")')
      account.updateBy = 'admin'
      
      return {
        code: 200,
        message: '修改成功'
      }
    }
  },

  // 删除账号
  {
    url: '/dev-api/sys/account/delete',
    type: 'post',
    response: config => {
      const { id } = config.body
      
      const accountIndex = accountList.findIndex(item => item.id === id)
      if (accountIndex === -1) {
        return {
          code: 400,
          message: '账号不存在'
        }
      }
      
      accountList.splice(accountIndex, 1)
      
      return {
        code: 200,
        message: '删除成功'
      }
    }
  },

  // 获取角色列表
  {
    url: '/dev-api/sys/account/roles',
    type: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: roleList
      }
    }
  }
]
